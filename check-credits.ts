import 'dotenv/config';
import { config } from 'dotenv';
import { findUserByEmail } from './src/models/user';
import { getUserValidCredits, getCreditsByUserUuid } from './src/models/credit';
import { getUserCredits } from './src/services/credit';

// Load environment variables
config({ path: '.env.development' });
config({ path: '.env.local' });
config({ path: '.env' });

async function checkUserCredits() {
  try {
    console.log('Checking user credits...');

    // First, let's see what users exist
    const testUser1 = await findUserByEmail('<EMAIL>');
    const testUser2 = await findUserByEmail('<EMAIL>');

    console.log('Test user 1:', testUser1 ? 'EXISTS' : 'NOT FOUND');
    console.log('Test user 2:', testUser2 ? 'EXISTS' : 'NOT FOUND');

    // Use the first available user
    const user = testUser1 || testUser2;
    if (!user) {
      console.log('❌ No test users found');
      return;
    }
    
    console.log('User info:', {
      uuid: user.uuid,
      email: user.email,
      created_at: user.created_at,
      credits: user.credits // This is the credits field in users table
    });
    
    // Check credits transactions
    console.log('\n--- Credits Transactions ---');
    const allCredits = await getCreditsByUserUuid(user.uuid);
    console.log('All credits transactions:', allCredits);
    
    // Check valid credits
    console.log('\n--- Valid Credits ---');
    const validCredits = await getUserValidCredits(user.uuid);
    console.log('Valid credits:', validCredits);
    
    // Check calculated credits
    console.log('\n--- Calculated Credits ---');
    const calculatedCredits = await getUserCredits(user.uuid);
    console.log('Calculated credits:', calculatedCredits);
    
  } catch (error) {
    console.error('❌ Error checking credits:', error);
    console.error('Error details:', error instanceof Error ? error.message : String(error));
  }
  
  process.exit(0);
}

checkUserCredits();
