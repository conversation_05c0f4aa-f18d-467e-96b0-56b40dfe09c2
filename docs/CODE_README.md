# LoopCraft - 代码文档

## 项目概述

LoopCraft 是一个专业的 AI 驱动的无缝音乐循环生成平台，基于 Next.js 构建。专注于为内容创作者、开发者和创意工作者提供高质量的循环背景音乐生成服务。该项目集成了用户认证、支付系统、积分管理、AI 音乐生成、音频分析等核心功能，为开发者提供了一个完整的音乐生成 SaaS 应用基础架构。

**项目状态**: ✅ 已完成并优化 - 所有核心功能已实现，音乐生成系统已优化为 Volcengine 主导架构，完全隐藏 AI Provider 选择，支持生产环境部署。

### 核心特性

- 🚀 **现代化技术栈**: Next.js 15 + React 19 + TypeScript
- 🎨 **优雅的 UI**: Tailwind CSS + Shadcn/ui 组件库，音乐主题设计
- 🔐 **完整的认证系统**: NextAuth.js 支持多种登录方式
- 💳 **支付集成**: Stripe 支付系统，支持订阅和一次性付款
- 🌍 **国际化支持**: next-intl 多语言支持，英文优先
- 🎵 **AI 音乐生成**: 支持 Mubert、Suno、Volcengine 等专业音乐生成 AI
- 🔄 **无缝循环验证**: 自动检测和验证音乐循环质量
- 📊 **音频分析**: 波形生成、BPM 检测、音调识别
- 🎯 **积分系统**: 完整的用户积分管理和消费机制
- 📈 **分析统计**: 内置数据分析和图表展示
- 🔑 **API 密钥管理**: 支持 API 密钥认证
- 🎧 **音乐播放器**: 内置音频播放器和波形可视化
- 📁 **收藏管理**: 音乐收藏夹和分类管理

## 技术栈

### 前端技术
- **框架**: Next.js 15.2.3 (App Router)
- **UI 库**: React 19.0.0
- **样式**: Tailwind CSS 4.1.4
- **组件库**: Shadcn/ui (基于 Radix UI)
- **状态管理**: React Context
- **表单处理**: React Hook Form + Zod 验证
- **动画**: Framer Motion
- **图表**: Recharts
- **通知**: Sonner
- **主题**: next-themes

### 后端技术
- **运行时**: Node.js
- **数据库**: PostgreSQL
- **ORM**: Drizzle ORM 0.44.2
- **认证**: NextAuth.js 5.0.0-beta.25
- **支付**: Stripe
- **文件存储**: AWS S3
- **AI SDK**: Vercel AI SDK

### AI 音乐生成集成
- **Mubert**: 快速音乐生成，支持多种风格和情绪
- **Suno**: 高质量音乐生成，支持分轨和变奏
- **Volcengine**: 火山引擎音乐生成服务，提供专业级音乐创作
- **音频分析**: 自研音频处理和循环验证算法

### 开发工具
- **语言**: TypeScript 5.7.2
- **包管理**: pnpm
- **代码规范**: ESLint
- **构建工具**: Next.js 内置
- **部署**: Vercel / Docker

## 项目架构

### 架构特点

LoopCraft 采用现代化的全栈架构设计，具有以下特点：

1. **分层架构**：
   - **表现层**：React 组件 + Tailwind CSS
   - **业务逻辑层**：Services 服务层
   - **数据访问层**：Models + Drizzle ORM
   - **数据存储层**：PostgreSQL + AWS S3

2. **模块化设计**：
   - 功能模块独立，职责分离明确
   - 组件化开发，提高代码复用性
   - 服务层封装业务逻辑，便于维护

3. **类型安全**：
   - 全面使用 TypeScript 进行类型检查
   - Drizzle ORM 提供数据库类型安全
   - Zod 进行运行时数据验证

4. **现代化技术栈**：
   - Next.js 15 App Router 提供最佳性能
   - React 19 最新特性支持
   - 服务端渲染 (SSR) 和静态生成 (SSG)

### 目录结构

```
loopcraft/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── [locale]/          # 国际化路由
│   │   │   ├── (admin)/       # 管理后台
│   │   │   ├── (default)/     # 默认布局
│   │   │   │   ├── (console)/ # 用户控制台
│   │   │   │   │   ├── api-keys/ # API密钥管理
│   │   │   │   │   ├── my-credits/ # 积分管理
│   │   │   │   │   ├── my-invites/ # 邀请管理
│   │   │   │   │   └── my-orders/ # 订单管理
│   │   │   │   ├── explore/   # 音乐探索页面
│   │   │   │   ├── gallery/   # 音乐画廊
│   │   │   │   ├── generate/  # 音乐生成页面
│   │   │   │   ├── library/   # 音乐库
│   │   │   │   ├── loops/     # 循环音乐详情
│   │   │   │   │   └── [slug]/ # 动态路由
│   │   │   │   ├── posts/     # 博客文章
│   │   │   │   ├── pricing/   # 定价页面
│   │   │   │   └── showcase/  # 展示页面
│   │   │   └── auth/          # 认证页面
│   │   ├── api/               # API 路由
│   │   │   ├── auth/          # 认证 API
│   │   │   │   └── [...nextauth]/ # NextAuth处理
│   │   │   ├── checkout/      # 支付 API
│   │   │   ├── music/         # 音乐生成 API
│   │   │   │   ├── analyze/   # 音频分析
│   │   │   │   ├── check-status/ # 状态检查
│   │   │   │   ├── download/  # 音乐下载
│   │   │   │   ├── generate/  # 音乐生成
│   │   │   │   ├── generations/ # 生成历史
│   │   │   │   ├── license/   # 授权证书
│   │   │   │   ├── providers/ # 提供商信息
│   │   │   │   ├── status/    # 生成状态
│   │   │   │   ├── stems/     # 分轨处理
│   │   │   │   ├── tracks/    # 音乐列表
│   │   │   │   ├── variations/ # 音乐变奏
│   │   │   │   ├── verify-loop/ # 循环验证
│   │   │   │   └── waveform/  # 波形生成
│   │   │   ├── demo/          # AI演示功能
│   │   │   │   ├── gen-image/ # 图像生成
│   │   │   │   ├── gen-stream-text/ # 流式文本
│   │   │   │   └── gen-text/  # 文本生成
│   │   │   ├── debug/         # 调试端点
│   │   │   ├── download/      # 通用下载
│   │   │   ├── user/          # 用户相关
│   │   │   │   └── credits/   # 用户积分
│   │   │   └── ...            # 其他 API
│   │   ├── globals.css        # 全局样式
│   │   ├── theme.css          # 音乐主题样式
│   │   └── layout.tsx         # 根布局
│   ├── components/            # React 组件
│   │   ├── blocks/           # 页面块组件
│   │   │   ├── music-hero/   # 音乐主页组件
│   │   │   └── music-features/ # 音乐功能介绍
│   │   ├── music/            # 音乐相关组件
│   │   │   ├── player/       # 音乐播放器
│   │   │   │   ├── audio-player.tsx # 基础播放器
│   │   │   │   └── loop-test-player.tsx # 循环测试播放器
│   │   │   ├── generator/    # 音乐生成器
│   │   │   │   └── music-generator-page.tsx # 生成页面
│   │   │   ├── waveform/     # 波形显示
│   │   │   │   └── waveform-display.tsx # 波形组件
│   │   │   ├── track/        # 音乐轨道组件
│   │   │   │   └── track-detail-page.tsx # 轨道详情
│   │   │   ├── stems/        # 分轨管理
│   │   │   │   └── stem-manager.tsx # 分轨管理器
│   │   │   └── variations/   # 变奏管理
│   │   │       └── variation-manager.tsx # 变奏管理器
│   │   ├── console/          # 控制台组件
│   │   ├── dashboard/        # 仪表板组件
│   │   ├── ui/               # UI 基础组件
│   │   │   ├── button.tsx    # 按钮组件
│   │   │   ├── card.tsx      # 卡片组件
│   │   │   ├── tech-card.tsx # 科技风卡片
│   │   │   ├── tech-button.tsx # 科技风按钮
│   │   │   ├── sonner.tsx    # 通知组件
│   │   │   └── ...           # 其他UI组件
│   │   └── ...               # 其他组件
│   ├── contexts/             # React Context
│   ├── db/                   # 数据库相关
│   │   ├── schema.ts         # 数据库模式（包含音乐表）
│   │   ├── config.ts         # 数据库配置
│   │   └── migrations/       # 数据库迁移
│   ├── hooks/                # 自定义 Hooks
│   ├── i18n/                 # 国际化
│   │   ├── messages/         # 翻译文件（英文优先）
│   │   └── pages/            # 页面翻译
│   ├── lib/                  # 工具库
│   ├── models/               # 数据模型
│   │   ├── music-generation.ts # 音乐生成模型
│   │   ├── track.ts          # 音乐轨道模型
│   │   ├── loop-verification.ts # 循环验证模型
│   │   ├── track-variation.ts # 音乐变奏模型
│   │   ├── track-stem.ts     # 分轨模型
│   │   ├── collection.ts     # 收藏夹模型
│   │   ├── user.ts           # 用户模型
│   │   ├── credit.ts         # 积分模型
│   │   └── order.ts          # 订单模型
│   ├── services/             # 业务逻辑
│   │   ├── music.ts          # 音乐服务
│   │   ├── music-generation.ts # 音乐生成服务
│   │   ├── music-provider.ts # 音乐提供商服务
│   │   ├── music-worker.ts   # 音乐生成后台任务
│   │   ├── audio-analysis.ts # 音频分析服务
│   │   ├── user.ts           # 用户服务
│   │   ├── credit.ts         # 积分服务
│   │   └── order.ts          # 订单服务
│   ├── types/                # TypeScript 类型
│   │   ├── music.d.ts        # 音乐相关类型
│   │   ├── music-api.d.ts    # 音乐 API 类型
│   │   ├── user.d.ts         # 用户类型
│   │   └── ...               # 其他类型定义
│   ├── aisdk/                # AI SDK 集成
│   │   └── music/            # 音乐 AI SDK
│   │       ├── providers/    # 音乐提供商
│   │       ├── music-model.ts # 音乐模型
│   │       └── generate-music.ts # 音乐生成
│   ├── auth/                 # 认证配置
│   │   └── config.ts         # NextAuth配置
│   └── middleware.ts         # 中间件
├── public/                   # 静态资源
├── docs/                     # 文档
│   ├── PRD.md               # 产品需求文档
│   ├── CODE_README.md       # 代码文档
│   └── MANUAL_TEST_PLAN.md  # 测试计划
├── debug/                    # 调试文件
├── components.json           # Shadcn/ui 配置
├── next.config.mjs          # Next.js 配置
├── tailwind.config.ts       # Tailwind 配置
├── tsconfig.json            # TypeScript 配置
├── package.json             # 依赖配置
├── jest.config.js           # Jest测试配置
├── Dockerfile               # Docker 配置
├── .env.example             # 环境变量示例
└── vercel.json              # Vercel 配置
```

### 架构层次

```mermaid
graph TB
    A[前端 UI 层] --> B[API 路由层]
    B --> C[服务层 Services]
    C --> D[模型层 Models]
    D --> E[数据库层 Database]

    F[认证中间件] --> B
    G[国际化中间件] --> A
    H[AI SDK] --> C
    I[支付系统] --> C
    J[文件存储] --> C
```

## 核心模块说明

### 1. 认证系统 (Authentication)

**位置**: `src/auth/`

认证系统基于 NextAuth.js 实现，支持多种登录方式：

- Google OAuth
- GitHub OAuth
- 凭证登录（Google One Tap）

**核心文件**:
- `config.ts`: 认证配置和提供商设置
- `handler.ts`: 登录处理逻辑
- `session.tsx`: 会话管理组件

**关键特性**:
- 自动用户创建和积分分配
- IP 地址记录
- 邀请码系统集成

### 2. 数据库层 (Database)

**位置**: `src/db/` 和 `src/models/`

使用 Drizzle ORM 管理 PostgreSQL 数据库：

**数据表结构**:
- `users`: 用户信息（包含订阅状态、积分字段）
- `orders`: 订单记录（Stripe支付集成）
- `credits`: 积分交易记录
- `apikeys`: API 密钥管理
- `posts`: 文章内容（博客系统）
- `affiliates`: 推广记录
- `feedbacks`: 用户反馈
- `music_generations`: 音乐生成请求记录
- `tracks`: 生成的音乐轨道信息
- `track_variations`: 音乐变奏记录
- `track_stems`: 分轨文件记录
- `collections`: 用户收藏夹
- `collection_tracks`: 收藏夹与音乐的关联

**模型层功能**:
- CRUD 操作封装
- 数据验证和类型安全
- 查询优化

### 3. 业务服务层 (Services)

**位置**: `src/services/`

封装核心业务逻辑：

- `user.ts`: 用户管理服务
- `credit.ts`: 积分系统服务
- `order.ts`: 订单处理服务
- `affiliate.ts`: 推广系统服务
- `page.ts`: 页面内容服务

### 4. AI 集成 (AI SDK)

**位置**: `src/aisdk/` 和 `src/services/`

集成多种 AI 服务提供商：

**音乐生成服务**:
- **Mubert**: 快速音乐生成，支持多种风格和情绪
- **Suno**: 高质量音乐生成，支持分轨和变奏
- **Volcengine**: 火山引擎音乐生成服务，提供专业级音乐创作

**其他 AI 服务**:
- 文本生成（OpenAI、DeepSeek、OpenRouter）
- 图像生成（OpenAI DALL-E、Replicate、Kling）
- 视频生成（Kling）
- 流式响应支持

### 5. 支付系统 (Payment)

**位置**: `src/app/api/checkout/`

基于 Stripe 的完整支付解决方案：

- 一次性付款
- 订阅付款
- 多币种支持（USD、CNY）
- Webhook 处理
- 订单状态管理

### 6. 积分系统 (Credits)

**位置**: `src/services/credit.ts`

完整的用户积分管理：

- 积分获取（新用户奖励、付费充值）
- 积分消费（API 调用、功能使用）
- 积分过期管理
- 交易记录追踪

### 7. API 密钥系统

**位置**: `src/models/apikey.ts`

支持 API 密钥认证：

- 密钥生成和管理
- Bearer Token 认证
- 用户权限验证

## API 接口文档

### 认证相关

#### POST /api/auth/[...nextauth]
NextAuth.js 认证端点，支持Google OAuth、GitHub OAuth和凭证登录

#### POST /api/get-user-info
获取用户信息
```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "uuid": "user-uuid",
    "email": "<EMAIL>",
    "nickname": "用户昵称",
    "credits": {
      "left_credits": 100,
      "is_pro": true
    }
  }
}
```

### 积分相关

#### POST /api/get-user-credits
获取用户积分
```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "left_credits": 100,
    "is_pro": true,
    "is_recharged": true
  }
}
```

#### POST /api/user/credits
用户积分管理API

### 支付相关

#### POST /api/checkout
创建支付会话
```json
// 请求
{
  "product_id": "basic_plan",
  "amount": 999,
  "currency": "usd",
  "interval": "month"
}

// 响应
{
  "code": 0,
  "message": "ok",
  "data": {
    "session_id": "cs_xxx",
    "public_key": "pk_xxx"
  }
}
```

#### POST /api/stripe-notify
Stripe Webhook处理端点

### 音乐生成相关

#### POST /api/music/generate
音乐生成主要端点
```json
// 请求
{
  "prompt": "upbeat electronic dance music",
  "duration": 30,
  "bpm": 120,
  "style": "electronic",
  "mood": "energetic"
}

// 响应
{
  "code": 0,
  "message": "ok",
  "data": {
    "generation_uuid": "gen-xxx",
    "task_id": "task-xxx",
    "estimated_time": 60
  }
}
```

#### GET /api/music/status/[requestId]
查询音乐生成状态
```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "status": "completed",
    "progress": 100,
    "track_uuid": "track-xxx",
    "file_url": "https://example.com/track.mp3"
  }
}
```

#### GET /api/music/providers
获取可用的音乐生成提供商
```json
{
  "providers": [
    {
      "name": "volcano",
      "display_name": "Volcengine",
      "description": "Professional AI music generation",
      "features": ["High Quality", "Fast Generation", "Multiple Styles"],
      "available": true
    }
  ],
  "default": "volcano",
  "total": 1
}
```

#### GET /api/music/tracks
获取音乐轨道列表
```json
{
  "code": 0,
  "message": "ok",
  "data": {
    "tracks": [...],
    "pagination": {
      "total": 100,
      "page": 1,
      "per_page": 20,
      "total_pages": 5,
      "has_more": true
    },
    "filters": {
      "available_styles": ["electronic", "pop", "rock"],
      "available_moods": ["happy", "energetic", "calm"],
      "bpm_range": { "min": 60, "max": 200 }
    }
  }
}
```

#### POST /api/music/analyze
音频分析端点
```json
// 请求
{
  "track_uuid": "track-xxx",
  "analysis_type": "full" // bpm, key, waveform, full
}

// 响应
{
  "code": 0,
  "message": "ok",
  "data": {
    "track_uuid": "track-xxx",
    "bpm": 120,
    "key": "C major",
    "waveform_data": [...],
    "has_analysis": true
  }
}
```

#### POST /api/music/verify-loop
循环验证端点
```json
// 请求
{
  "track_uuid": "track-xxx"
}

// 响应
{
  "code": 0,
  "message": "ok",
  "data": {
    "is_seamless": true,
    "loop_quality_score": 9.2,
    "verification_details": {...}
  }
}
```

#### GET /api/music/download/[trackId]
音乐下载端点，支持不同格式和质量

#### POST /api/music/stems
分轨处理端点（Pro功能）

#### POST /api/music/variations
音乐变奏生成端点（Pro功能）

#### POST /api/music/waveform
波形生成端点

#### GET /api/music/generations
获取用户音乐生成历史

#### POST /api/music/license
音乐授权证书生成

### AI Demo相关

#### POST /api/demo/gen-text
文本生成演示
```json
// 请求
{
  "prompt": "写一首诗",
  "provider": "openai",
  "model": "gpt-4"
}

// 响应
{
  "code": 0,
  "message": "ok",
  "data": {
    "text": "生成的文本内容",
    "reasoning": "推理过程"
  }
}
```

#### POST /api/demo/gen-image
图像生成演示
```json
// 请求
{
  "prompt": "a beautiful landscape",
  "provider": "openai",
  "model": "dall-e-3"
}

// 响应
{
  "code": 0,
  "message": "ok",
  "data": [
    {
      "url": "https://example.com/image.png",
      "filename": "generated_image.png"
    }
  ]
}
```

#### POST /api/demo/gen-stream-text
流式文本生成演示

### 其他功能相关

#### POST /api/add-feedback
提交用户反馈
```json
// 请求
{
  "content": "反馈内容",
  "rating": 5
}

// 响应
{
  "code": 0,
  "message": "ok",
  "data": {
    "id": 1,
    "status": "created"
  }
}
```

#### POST /api/update-invite-code
更新邀请码
```json
// 请求
{
  "invite_code": "my_code"
}

// 响应
{
  "code": 0,
  "message": "ok",
  "data": {
    "invite_code": "my_code"
  }
}
```

#### GET /api/download/[trackId]
通用下载端点

#### POST /api/ping
API 连通性测试
```json
// 请求
{
  "message": "hello"
}

// 响应
{
  "code": 0,
  "message": "ok",
  "data": {
    "pong": "received message: hello"
  }
}
```

### 调试和测试相关

#### /api/debug/*
调试相关端点：
- `/api/debug/env` - 环境变量检查
- `/api/debug/generations` - 生成记录调试
- `/api/debug/tracks` - 音轨数据调试
- `/api/debug/music-test` - 音乐生成测试
- `/api/debug/test-audio` - 音频处理测试

## 部署指南

### 环境变量配置

创建 `.env.development` 文件（用户偏好使用.env.development而非.env.local）：

```bash
# 基础配置
NEXT_PUBLIC_WEB_URL="http://localhost:3000"
NEXT_PUBLIC_PROJECT_NAME="LoopCraft"

# 数据库
DATABASE_URL="postgresql://username:password@localhost:5432/database"

# NextAuth认证
AUTH_SECRET="Zt3BXVudzzRq2R2WBqhwRy1dNMq48Gg9zKAYq7YwSL0="
AUTH_URL="http://localhost:3000/api/auth"
AUTH_TRUST_HOST=true

# Google OAuth
AUTH_GOOGLE_ID="your-google-client-id"
AUTH_GOOGLE_SECRET="your-google-client-secret"
NEXT_PUBLIC_AUTH_GOOGLE_ID="your-google-client-id"
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED="true"
NEXT_PUBLIC_AUTH_GOOGLE_ONE_TAP_ENABLED="true"

# GitHub OAuth
AUTH_GITHUB_ID="your-github-client-id"
AUTH_GITHUB_SECRET="your-github-client-secret"
NEXT_PUBLIC_AUTH_GITHUB_ENABLED="true"

# Stripe支付
STRIPE_PUBLIC_KEY="pk_test_xxx"
STRIPE_PRIVATE_KEY="sk_test_xxx"
NEXT_PUBLIC_STRIPE_PUBLIC_KEY="pk_test_xxx"

# 音乐生成服务（主要提供商：Volcengine）
VOLCANO_ACCESS_KEY_ID="your-volcano-access-key"
VOLCANO_SECRET_ACCESS_KEY="your-volcano-secret-key"
VOLCANO_REGION="cn-beijing"
VOLCANO_SERVICE_NAME="imagination"
VOLCANO_BASE_URL="https://open.volcengineapi.com"
VOLCANO_VERSION="2024-08-12"

# 备用音乐生成服务
MUBERT_API_KEY="your-mubert-api-key"
MUBERT_BASE_URL="https://api-b2b.mubert.com/v2"
SUNO_API_KEY="your-suno-api-key"
SUNO_BASE_URL="https://api.suno.ai/v1"

# 音乐提供商配置
NEXT_PUBLIC_PRIMARY_MUSIC_PROVIDER="volcano"
NEXT_PUBLIC_SHOW_PROVIDER_SELECTION="false"  # 隐藏提供商选择

# AI 演示服务
OPENAI_API_KEY="sk-xxx"
DEEPSEEK_API_KEY="sk-xxx"
REPLICATE_API_TOKEN="r8_xxx"
KLING_ACCESS_KEY="xxx"
KLING_SECRET_KEY="xxx"
OPENROUTER_API_KEY="sk-or-xxx"
SILICONFLOW_API_KEY="sk-xxx"
SILICONFLOW_BASE_URL="https://api.siliconflow.cn/v1"

# AWS S3存储
AWS_ACCESS_KEY_ID="xxx"
AWS_SECRET_ACCESS_KEY="xxx"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="your-bucket"
NEXT_PUBLIC_AWS_S3_BUCKET="your-bucket"
NEXT_PUBLIC_AWS_REGION="us-east-1"

# 应用配置
NEXT_PUBLIC_PAY_CANCEL_URL="http://localhost:3000"
NEXT_PUBLIC_DEFAULT_LOCALE="en"

# 分析和监控
NEXT_PUBLIC_GOOGLE_ADCODE="your-google-ads-code"

# 开发环境特定
NODE_ENV="development"
NEXT_PUBLIC_NODE_ENV="development"
```

### 本地开发

1. 克隆项目
```bash
git clone https://github.com/opcraft/opcraft-ai.git
cd opcraft-ai
```

2. 安装依赖
```bash
pnpm install
```

3. 配置环境变量
```bash
cp .env.example .env.local
# 编辑 .env.local 文件
```

4. 初始化数据库
```bash
pnpm db:generate
pnpm db:migrate
```

5. 启动开发服务器
```bash
pnpm dev
```

访问 http://localhost:3000 查看应用

### Docker 部署

1. 构建镜像
```bash
docker build -t opcraft-ai .
```

2. 运行容器
```bash
docker run -p 3000:3000 \
  -e DATABASE_URL="your-database-url" \
  -e NEXTAUTH_SECRET="your-secret" \
  -e OPENAI_API_KEY="your-openai-key" \
  opcraft-ai
```

### Vercel 部署

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 部署项目

项目已配置 `vercel.json` 文件，支持：
- API 函数超时设置（60秒）
- 自动部署优化

### 生产环境注意事项

1. **数据库**: 使用生产级 PostgreSQL 数据库
2. **环境变量**: 确保所有敏感信息通过环境变量配置
3. **域名配置**: 更新 `NEXTAUTH_URL` 为生产域名
4. **SSL 证书**: 确保 HTTPS 配置正确
5. **监控**: 配置应用性能监控和错误追踪

## 开发指南

### 代码规范

项目遵循以下代码规范：

1. **组件命名**: 使用 PascalCase
2. **文件命名**: 使用 kebab-case
3. **函数命名**: 使用 camelCase
4. **常量命名**: 使用 UPPER_SNAKE_CASE

### 项目约定

1. **组件结构**:
   - 优先使用函数组件
   - 使用 TypeScript 进行类型定义
   - 组件文件包含 `index.tsx`

2. **API 设计**:
   - 统一的响应格式 (`respData`, `respErr`)
   - 错误处理和日志记录
   - 参数验证

3. **数据库操作**:
   - 使用 Models 层封装 CRUD
   - 类型安全的查询
   - 事务处理

### 添加新功能

1. **添加新的 API 端点**:
```typescript
// src/app/api/new-feature/route.ts
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";

export async function POST(req: Request) {
  try {
    const { param } = await req.json();

    // 验证用户权限
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    // 业务逻辑处理
    const result = await processBusinessLogic(param);

    return respData(result);
  } catch (error) {
    console.log("new feature error:", error);
    return respErr("operation failed");
  }
}
```

2. **添加新的数据模型**:
```typescript
// src/db/schema.ts
export const newTable = pgTable("new_table", {
  id: integer().primaryKey().generatedAlwaysAsIdentity(),
  user_uuid: varchar({ length: 255 }).notNull(),
  created_at: timestamp({ withTimezone: true }),
  // 其他字段定义
});

// src/models/new-model.ts
import { newTable } from "@/db/schema";
import { db } from "@/db";
import { eq } from "drizzle-orm";

export async function insertNewRecord(data: typeof newTable.$inferInsert) {
  const [record] = await db().insert(newTable).values(data).returning();
  return record;
}

export async function findRecordByUuid(uuid: string) {
  const [record] = await db()
    .select()
    .from(newTable)
    .where(eq(newTable.user_uuid, uuid))
    .limit(1);
  return record;
}
```

3. **添加新的服务**:
```typescript
// src/services/new-service.ts
import { insertNewRecord, findRecordByUuid } from "@/models/new-model";

export async function createNewRecord(user_uuid: string, data: any) {
  try {
    const record = await insertNewRecord({
      user_uuid,
      ...data,
      created_at: new Date(),
    });

    return record;
  } catch (error) {
    console.log("create record failed:", error);
    throw error;
  }
}

export async function getUserRecords(user_uuid: string) {
  try {
    const records = await findRecordByUuid(user_uuid);
    return records;
  } catch (error) {
    console.log("get records failed:", error);
    throw error;
  }
}
```

4. **添加新的前端组件**:
```typescript
// src/components/new-feature/index.tsx
"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";

export default function NewFeature() {
  const [loading, setLoading] = useState(false);

  const handleAction = async () => {
    try {
      setLoading(true);

      const response = await fetch("/api/new-feature", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ param: "value" }),
      });

      const { code, message, data } = await response.json();

      if (code !== 0) {
        toast.error(message);
        return;
      }

      toast.success("操作成功");
    } catch (error) {
      toast.error("操作失败");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Button onClick={handleAction} disabled={loading}>
        {loading ? "处理中..." : "执行操作"}
      </Button>
    </div>
  );
}
```

### 测试

项目包含 API 测试文件：
- `debug/apitest.http`: HTTP 请求测试

建议添加单元测试和集成测试：

```typescript
// __tests__/api/new-feature.test.ts
import { POST } from "@/app/api/new-feature/route";

describe("/api/new-feature", () => {
  it("should handle valid request", async () => {
    const request = new Request("http://localhost:3000/api/new-feature", {
      method: "POST",
      body: JSON.stringify({ param: "test" }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(data.code).toBe(0);
  });
});
```

### 性能优化

1. **图片优化**: 使用 Next.js Image 组件
2. **代码分割**: 动态导入大型组件
3. **缓存策略**: 合理使用 React Query 或 SWR
4. **数据库优化**: 添加适当的索引和查询优化
5. **Bundle 分析**: 使用 `pnpm analyze` 分析包大小

## 代码质量评估

### 优点

1. **架构清晰**:
   - 分层架构，职责分离明确
   - Models、Services、API 层次分明
   - 组件化设计，复用性好

2. **类型安全**:
   - 全面使用 TypeScript
   - Drizzle ORM 提供类型安全的数据库操作
   - Zod 进行运行时类型验证

3. **现代化技术栈**:
   - 使用最新的 React 19 和 Next.js 15 特性
   - App Router 提供更好的性能和开发体验
   - 集成了最新的 AI SDK

4. **完整功能**:
   - 包含认证、支付、AI 集成等完整功能
   - 积分系统和 API 密钥管理
   - 国际化和主题支持

5. **开发体验**:
   - 良好的开发工具配置
   - 清晰的项目结构
   - 详细的类型定义

### 改进建议

1. **测试覆盖**:
   - 缺少单元测试和集成测试
   - 建议添加 Jest 和 React Testing Library
   - API 端点需要更全面的测试

2. **错误处理**:
   - 可以更细化的错误分类和处理
   - 建议添加全局错误边界
   - 统一的错误码定义

3. **日志系统**:
   - 当前使用 console.log，建议使用结构化日志
   - 添加日志级别和分类
   - 生产环境日志收集

4. **API 文档**:
   - 可以集成 Swagger 或类似工具
   - 自动生成 API 文档
   - 添加请求/响应示例

5. **监控告警**:
   - 添加应用性能监控（APM）
   - 错误追踪和告警
   - 用户行为分析

6. **安全加固**:
   - 添加 API 调用频率限制
   - 输入验证和 SQL 注入防护
   - CSRF 和 XSS 防护

### 代码异味识别

1. **重复代码**:
   - API 路由中的错误处理模式可以抽象
   - 数据库查询模式可以进一步封装

2. **硬编码**:
   - 一些常量可以移到配置文件
   - 魔法数字需要定义为常量

3. **函数复杂度**:
   - 部分函数过长，建议拆分
   - 嵌套层级可以优化

### 安全考虑

1. **输入验证**:
   - 使用 Zod 进行严格的输入验证
   - API 参数校验完善

2. **认证授权**:
   - NextAuth.js 提供安全的认证机制
   - API 密钥和会话管理安全

3. **数据保护**:
   - 敏感信息通过环境变量管理
   - 数据库连接加密

4. **建议改进**:
   - 添加 API 限流机制
   - 实现更细粒度的权限控制
   - 添加审计日志

## 数据流图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant S as 服务层
    participant M as 模型层
    participant D as 数据库

    U->>F: 用户操作
    F->>A: HTTP 请求
    A->>A: 认证验证
    A->>S: 调用业务逻辑
    S->>M: 数据操作
    M->>D: SQL 查询
    D->>M: 返回数据
    M->>S: 返回结果
    S->>A: 返回处理结果
    A->>F: JSON 响应
    F->>U: 更新界面
```

## 总结

LoopCraft AI 是一个功能完整、架构清晰的 AI 音乐生成应用。它提供了构建现代音乐生成 SaaS 应用所需的所有核心功能，包括：

### 主要优势

1. **快速启动**: 开箱即用的完整功能
2. **技术先进**: 使用最新的技术栈
3. **架构合理**: 清晰的分层架构
4. **扩展性强**: 易于添加新功能
5. **类型安全**: 全面的 TypeScript 支持

### 适用场景

- AI 音乐生成平台
- 循环背景音乐服务
- 内容创作工具
- 音频 API 服务平台
- 订阅制音乐应用

### 学习价值

通过本项目，开发者可以学习到：

1. **现代 Web 开发**: Next.js 15 + React 19 最佳实践
2. **全栈开发**: 前后端一体化开发模式
3. **AI 集成**: 多种 AI 服务的集成方法
4. **支付系统**: Stripe 支付集成实现
5. **用户系统**: 完整的用户认证和管理
6. **数据库设计**: PostgreSQL + Drizzle ORM 使用

## 🔧 最新更新

### v1.0.0 完成版本 (2025-01-14)

**项目完成状态**：
- 🎉 **项目完全完成**：所有核心功能已实现并可正常运行
- ✅ **生产环境就绪**：支持 Vercel 和 Docker 部署
- ✅ **构建成功**：项目可成功构建并生成静态页面
- ✅ **代码文档更新**：基于完整代码扫描更新了实现文档

**完整的API路由系统**：
- ✅ **音乐生成API**：完整的音乐生成、状态查询、下载功能
- ✅ **音频分析API**：BPM检测、音调识别、波形生成
- ✅ **循环验证API**：无缝循环质量检测和评分
- ✅ **分轨和变奏API**：Pro用户高级功能支持
- ✅ **用户管理API**：积分、订单、认证完整功能
- ✅ **调试和测试API**：开发环境调试支持

**前端页面和组件**：
- 🎨 **完整的页面结构**：主页、生成、探索、画廊、定价等
- 🎵 **音乐相关组件**：播放器、生成器、波形显示、轨道管理
- 🎛️ **用户控制台**：API密钥、积分、邀请、订单管理
- 📱 **响应式设计**：支持桌面端和移动端
- 🎨 **科技风UI**：TechCard、TechButton等专业组件

**AI 音乐生成系统**：
- 🎵 **多提供商架构**：Volcengine主导，Mubert、Suno备用
- 🔄 **智能降级机制**：主要提供商不可用时自动切换
- 🎛️ **完全隐藏提供商选择**：用户无感知的后端切换
- 📊 **完整音频分析**：波形、BPM、音调、循环质量检测
- 🎧 **专业播放器**：循环测试播放器和标准音频播放器
- 🔄 **无缝循环验证**：自动检测和评分循环质量

**数据库和模型**：
- � **完整数据表结构**：用户、订单、积分、音乐生成、轨道等
- 🔄 **音乐生成流程**：从请求到完成的完整状态追踪
- 📁 **收藏和分类**：用户音乐库管理功能
- 🎵 **分轨和变奏**：Pro用户高级音乐功能支持
- 💳 **订阅和积分**：完整的商业化功能实现

**用户体验优化**：
- 🌍 **英文优先国际化**：针对西方市场的本地化
- 🎨 **音乐主题设计**：专业的音乐生成平台UI
- � **个性化定制**：BPM、风格、情绪等丰富选项
- 🔐 **完整认证系统**：Google、GitHub OAuth支持
- 💳 **Stripe支付集成**：订阅和一次性付款支持

**开发和部署**：
- 🛠️ **现代化技术栈**：Next.js 15 + React 19 + TypeScript
- 🧪 **测试框架配置**：Jest + Testing Library
- 🐳 **Docker支持**：容器化部署配置
- ☁️ **Vercel优化**：生产环境部署配置
- � **完整文档**：PRD、代码文档、测试计划

---

通过本文档，开发者可以快速理解项目结构，掌握核心功能的实现原理，并能够基于此平台快速开发自己的 AI 音乐生成产品。项目代码质量良好，架构设计合理，是学习和实践现代 AI 音乐生成应用开发的优秀案例。

**项目状态：✅ 完全就绪，可直接部署使用**