# 积分不足引导流程测试文档

## 概述

本文档描述了音乐生成页面积分不足引导功能的测试策略和测试用例。该功能确保用户在积分不足时能够获得清晰的引导，顺畅地完成积分购买并返回继续使用。

## 功能特性

### 核心功能
1. **积分状态检查**：实时检查用户积分余额
2. **积分不足提示**：当积分不足时显示友好的提示信息
3. **购买引导**：提供直接的购买积分链接
4. **购买后返回**：支付完成后自动返回生成页面
5. **实时更新**：页面焦点变化时自动刷新积分状态

### 用户体验优化
- 实时积分状态显示
- 选择不同时长时的即时反馈
- 视觉化的积分状态指示
- 响应式设计支持

## 测试策略

### 1. 单元测试 (Unit Tests)
**文件位置**: `src/components/music/generator/__tests__/credits-flow.test.tsx`

**测试范围**:
- 组件渲染和状态管理
- 积分检查逻辑
- 用户交互处理
- API调用模拟

**主要测试场景**:
- 积分充足时的正常显示
- 积分不足时的禁用状态
- 不同时长选择的积分计算
- 积分不足模态框的显示
- 页面焦点检测机制

### 2. 端到端测试 (E2E Tests)
**文件位置**: `tests/e2e/credits-flow.spec.ts`

**测试范围**:
- 完整的用户流程
- 跨页面交互
- 真实的浏览器行为
- 响应式设计验证

**主要测试场景**:
- 完整的积分不足引导流程
- 价格页面跳转和回调
- 购买后返回流程
- 移动端体验测试

## 测试用例详解

### 积分充足场景
```typescript
test('用户有足够积分时应显示正常的生成按钮', async () => {
  // 模拟用户有10个积分
  // 验证生成按钮可用
  // 验证积分状态显示正确
});
```

### 积分不足场景
```typescript
test('用户积分不足时应显示禁用的按钮和提示', async () => {
  // 模拟用户积分为0
  // 验证生成按钮被禁用
  // 验证积分不足提示显示
});
```

### 实时反馈测试
```typescript
test('选择不同时长时应显示相应的积分不足提示', async () => {
  // 测试15s (1积分)、30s (2积分)、60s (3积分)
  // 验证实时积分状态更新
  // 验证成本显示正确
});
```

### 购买引导测试
```typescript
test('点击"Get Credits"按钮应打开价格页面', async () => {
  // 验证新标签页打开
  // 验证URL包含回调参数
  // 验证价格页面正常加载
});
```

### 页面焦点检测测试
```typescript
test('页面获得焦点时应刷新积分', async () => {
  // 模拟页面焦点变化
  // 验证积分API被重新调用
  // 验证积分状态更新
});
```

## 运行测试

### 单元测试
```bash
# 运行所有单元测试
npm run test

# 运行特定的积分流程测试
npm run test -- credits-flow.test.tsx

# 运行测试并生成覆盖率报告
npm run test:coverage
```

### 端到端测试
```bash
# 安装 Playwright
npx playwright install

# 运行 E2E 测试
npm run test:e2e

# 运行特定的积分流程 E2E 测试
npx playwright test credits-flow.spec.ts

# 以调试模式运行测试
npx playwright test --debug credits-flow.spec.ts
```

## 测试数据和模拟

### API 模拟
```typescript
// 积分充足的模拟响应
{
  code: 0,
  data: { left_credits: 10 }
}

// 积分不足的模拟响应
{
  code: 0,
  data: { left_credits: 0 }
}

// 积分不足错误的模拟响应
{
  code: -1,
  message: 'Insufficient credits. Required: 2, Available: 0'
}
```

### 测试环境配置
- 使用 Jest 进行单元测试
- 使用 Playwright 进行 E2E 测试
- 使用 React Testing Library 进行组件测试
- Mock Next.js 路由和认证

## 测试覆盖率目标

- **组件覆盖率**: 95%+
- **功能覆盖率**: 100%
- **分支覆盖率**: 90%+
- **E2E 场景覆盖率**: 100%

## 持续集成

### GitHub Actions 配置
```yaml
name: Credits Flow Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
      - run: npm ci
      - run: npm run test:unit
      - run: npm run test:e2e
```

## 性能测试

### 关键指标
- 积分检查API响应时间 < 200ms
- 页面焦点检测响应时间 < 100ms
- 积分状态更新渲染时间 < 50ms

### 测试方法
```typescript
test('积分检查性能测试', async () => {
  const startTime = performance.now();
  await loadUserCredits();
  const endTime = performance.now();
  expect(endTime - startTime).toBeLessThan(200);
});
```

## 故障排除

### 常见问题
1. **测试超时**: 增加 waitFor 超时时间
2. **API模拟失败**: 检查 fetch mock 配置
3. **页面焦点检测失败**: 确保正确模拟 window 事件

### 调试技巧
- 使用 `screen.debug()` 查看组件渲染状态
- 使用 Playwright 的 `--debug` 模式进行 E2E 调试
- 检查控制台日志和网络请求

## 维护和更新

### 定期检查
- 每月检查测试用例的有效性
- 更新测试数据以反映实际使用情况
- 添加新功能的测试覆盖

### 测试用例更新
- 新增功能时同步添加测试
- 修复bug时添加回归测试
- 性能优化后更新性能测试基准
