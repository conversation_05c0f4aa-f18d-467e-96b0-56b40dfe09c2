// <PERSON>ript to check API parameter consistency
const fs = require('fs');

// API文档中的参数列表
const API_GENRES = ["corporate","dance/edm","orchestral","chill out","rock","hip hop","folk","funk","ambient","holiday","jazz","kids","world","travel","commercial","advertising","driving","cinematic","upbeat","epic","inspiring","business","video game","dark","pop","trailer","modern","electronic","documentary","soundtrack","fashion","acoustic","movie","tv","high tech","industrial","dance","video","vlog","marketing","game","radio","promotional","sports","party","summer","beauty"];

const API_MOODS = ["positive","uplifting","energetic","happy","bright","optimistic","hopeful","cool","dreamy","fun","light","powerful","calm","confident","joyful","dramatic","peaceful","playful","soft","groovy","reflective","easy","relaxed","lively","smooth","romantic","intense","elegant","mellow","emotional","sentimental","cheerful happy","contemplative","soothing","proud","passionate","sweet","mystical","tranquil","cheerful","casual","beautiful","ethereal","melancholy","sad","aggressive","haunting","adventure","serene","sincere","funky","funny"];

const API_INSTRUMENTS = ["piano","drums","guitar","percussion","synth","electric guitar","acoustic guitar","bass guitar","brass","violin","cello","flute","organ","trumpet","ukulele","saxophone","double bass","harp","glockenspiel","synthesizer","keyboard","marimba","bass","banjo","strings"];

const API_THEMES = ["inspirational","motivational","achievement","discovery","every day","love","technology","lifestyle","journey","meditation","drama","children","hope","fantasy","holiday","health","family","real estate","media","kids","science","education","progress","world","vacation","training","christmas","sales"];

// 读取我们的常量文件
const constantsFile = fs.readFileSync('src/constants/music-api.ts', 'utf8');

// 提取我们的常量数组
function extractArray(content, arrayName) {
  const regex = new RegExp(`export const ${arrayName} = \\[([\\s\\S]*?)\\] as const;`);
  const match = content.match(regex);
  if (match) {
    const arrayContent = match[1];
    // 提取所有引号中的字符串
    const items = arrayContent.match(/"([^"]*)"/g);
    return items ? items.map(item => item.slice(1, -1)) : [];
  }
  return [];
}

const OUR_GENRES = extractArray(constantsFile, 'MUSIC_GENRES');
const OUR_MOODS = extractArray(constantsFile, 'MUSIC_MOODS');
const OUR_INSTRUMENTS = extractArray(constantsFile, 'MUSIC_INSTRUMENTS');
const OUR_THEMES = extractArray(constantsFile, 'MUSIC_THEMES');

// 比对函数
function compareArrays(apiArray, ourArray, name) {
  console.log(`\n=== ${name} 比对 ===`);
  console.log(`API数量: ${apiArray.length}, 我们的数量: ${ourArray.length}`);
  
  // 检查我们缺少的
  const missing = apiArray.filter(item => !ourArray.includes(item));
  if (missing.length > 0) {
    console.log(`❌ 我们缺少的${name}:`, missing);
  }
  
  // 检查我们多出的
  const extra = ourArray.filter(item => !apiArray.includes(item));
  if (extra.length > 0) {
    console.log(`⚠️  我们多出的${name}:`, extra);
  }
  
  // 检查顺序
  const orderDiff = apiArray.some((item, index) => ourArray[index] !== item);
  if (orderDiff) {
    console.log(`⚠️  ${name}顺序不一致`);
  }
  
  if (missing.length === 0 && extra.length === 0) {
    console.log(`✅ ${name}完全匹配!`);
  }
  
  return { missing, extra, orderDiff };
}

// 执行比对
console.log('🔍 开始比对API参数...\n');

const genreResult = compareArrays(API_GENRES, OUR_GENRES, 'GENRES');
const moodResult = compareArrays(API_MOODS, OUR_MOODS, 'MOODS');
const instrumentResult = compareArrays(API_INSTRUMENTS, OUR_INSTRUMENTS, 'INSTRUMENTS');
const themeResult = compareArrays(API_THEMES, OUR_THEMES, 'THEMES');

// 总结
console.log('\n=== 总结 ===');
const allPerfect = [genreResult, moodResult, instrumentResult, themeResult]
  .every(result => result.missing.length === 0 && result.extra.length === 0);

if (allPerfect) {
  console.log('🎉 所有参数都完全匹配API文档!');
} else {
  console.log('❌ 存在不一致，需要修复');
}
