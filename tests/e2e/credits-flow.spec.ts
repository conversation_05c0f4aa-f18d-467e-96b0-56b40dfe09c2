/**
 * 积分不足引导流程端到端测试
 * 使用 Playwright 测试完整的用户体验流程
 */

import { test, expect } from '@playwright/test';

test.describe('积分不足引导流程 E2E 测试', () => {
  test.beforeEach(async ({ page }) => {
    // 导航到音乐生成页面
    await page.goto('/generate');
    
    // 等待页面加载完成
    await page.waitForLoadState('networkidle');
  });

  test('用户积分充足时的正常流程', async ({ page }) => {
    // 模拟用户有足够积分的情况
    await page.route('/api/get-user-credits', async route => {
      await route.fulfill({
        json: {
          code: 0,
          data: { left_credits: 10 }
        }
      });
    });

    await page.reload();
    await page.waitForLoadState('networkidle');

    // 验证积分显示
    await expect(page.locator('text=10')).toBeVisible();
    await expect(page.locator('text=credits')).toBeVisible();

    // 验证生成按钮可用
    const generateButton = page.getByRole('button', { name: /generate music/i });
    await expect(generateButton).toBeEnabled();

    // 验证积分状态提示
    await expect(page.locator('text=✓ You have enough credits')).toBeVisible();

    // 测试选择不同时长
    await page.click('button:has-text("15s")');
    await expect(page.locator('text=✓ You have enough credits (10/1)')).toBeVisible();

    await page.click('button:has-text("60s")');
    await expect(page.locator('text=✓ You have enough credits (10/3)')).toBeVisible();
  });

  test('用户积分不足时的引导流程', async ({ page }) => {
    // 模拟用户积分不足的情况
    await page.route('/api/get-user-credits', async route => {
      await route.fulfill({
        json: {
          code: 0,
          data: { left_credits: 0 }
        }
      });
    });

    await page.reload();
    await page.waitForLoadState('networkidle');

    // 验证积分显示
    await expect(page.locator('text=0')).toBeVisible();
    await expect(page.locator('text=credits')).toBeVisible();

    // 验证生成按钮被禁用
    const generateButton = page.getByRole('button', { name: /need more credits/i });
    await expect(generateButton).toBeDisabled();

    // 验证积分不足提示
    await expect(page.locator('text=⚠ Need 2 more credits to generate 30s music')).toBeVisible();
    await expect(page.locator('text=Need 2 more credits')).toBeVisible();

    // 测试选择不同时长的提示变化
    await page.click('button:has-text("15s")');
    await expect(page.locator('text=⚠ Need 1 more credit to generate 15s music')).toBeVisible();
    await expect(page.locator('text=Cost: 1 credits')).toBeVisible();

    await page.click('button:has-text("60s")');
    await expect(page.locator('text=⚠ Need 3 more credits to generate 60s music')).toBeVisible();
    await expect(page.locator('text=Cost: 3 credits')).toBeVisible();
  });

  test('点击"Get Credits"按钮跳转到价格页面', async ({ page, context }) => {
    // 模拟用户积分不足的情况
    await page.route('/api/get-user-credits', async route => {
      await route.fulfill({
        json: {
          code: 0,
          data: { left_credits: 0 }
        }
      });
    });

    await page.reload();
    await page.waitForLoadState('networkidle');

    // 点击"Get Credits"按钮
    const getCreditsButton = page.getByRole('button', { name: /get credits/i });
    
    // 监听新标签页打开
    const pagePromise = context.waitForEvent('page');
    await getCreditsButton.click();
    const newPage = await pagePromise;

    // 验证新标签页的URL包含回调参数
    await newPage.waitForLoadState();
    expect(newPage.url()).toContain('/pricing?callback=%2Fgenerate');

    // 验证价格页面加载正常
    await expect(newPage.locator('text=Simple, Transparent Pricing')).toBeVisible();
    await expect(newPage.locator('text=Create Professional Music in Minutes')).toBeVisible();
  });

  test('积分不足模态框的显示和跳转', async ({ page, context }) => {
    // 模拟用户积分不足的情况
    await page.route('/api/get-user-credits', async route => {
      await route.fulfill({
        json: {
          code: 0,
          data: { left_credits: 0 }
        }
      });
    });

    // 模拟音乐生成API返回积分不足错误
    await page.route('/api/music/generate', async route => {
      await route.fulfill({
        json: {
          code: -1,
          message: 'Insufficient credits. Required: 2, Available: 0'
        }
      });
    });

    await page.reload();
    await page.waitForLoadState('networkidle');

    // 填写表单
    await page.fill('textarea[placeholder*="music"]', 'Test music for credits validation');

    // 尝试提交表单（通过开发者工具或其他方式绕过禁用状态）
    await page.evaluate(() => {
      const form = document.querySelector('form');
      if (form) {
        const event = new Event('submit', { bubbles: true, cancelable: true });
        form.dispatchEvent(event);
      }
    });

    // 验证积分不足模态框显示
    await expect(page.locator('text=Insufficient Credits')).toBeVisible();
    await expect(page.locator('text=You need more credits to generate')).toBeVisible();

    // 点击"Get Credits"按钮
    const pagePromise = context.waitForEvent('page');
    await page.click('button:has-text("Get Credits")');
    const newPage = await pagePromise;

    // 验证跳转到价格页面并包含回调参数
    await newPage.waitForLoadState();
    expect(newPage.url()).toContain('/pricing?callback=%2Fgenerate');
  });

  test('页面焦点检测和积分刷新', async ({ page }) => {
    let creditsCallCount = 0;
    
    // 拦截积分API调用并计数
    await page.route('/api/get-user-credits', async route => {
      creditsCallCount++;
      await route.fulfill({
        json: {
          code: 0,
          data: { left_credits: creditsCallCount * 2 } // 模拟积分增加
        }
      });
    });

    await page.reload();
    await page.waitForLoadState('networkidle');

    // 验证初始积分
    await expect(page.locator('text=2')).toBeVisible();

    // 模拟页面失去焦点然后重新获得焦点
    await page.evaluate(() => window.blur());
    await page.evaluate(() => window.focus());

    // 等待积分刷新
    await page.waitForTimeout(1000);
    await expect(page.locator('text=4')).toBeVisible();

    // 验证API被调用了多次
    expect(creditsCallCount).toBeGreaterThan(1);
  });

  test('购买后返回流程模拟', async ({ page }) => {
    // 模拟从价格页面返回的场景
    await page.goto('/generate?purchased=true');
    
    // 模拟购买后积分增加
    await page.route('/api/get-user-credits', async route => {
      await route.fulfill({
        json: {
          code: 0,
          data: { left_credits: 10 }
        }
      });
    });

    await page.waitForLoadState('networkidle');

    // 验证积分已更新
    await expect(page.locator('text=10')).toBeVisible();
    await expect(page.locator('text=credits')).toBeVisible();

    // 验证生成按钮现在可用
    const generateButton = page.getByRole('button', { name: /generate music/i });
    await expect(generateButton).toBeEnabled();

    // 验证积分状态提示
    await expect(page.locator('text=✓ You have enough credits')).toBeVisible();
  });

  test('响应式设计和移动端体验', async ({ page }) => {
    // 设置移动端视口
    await page.setViewportSize({ width: 375, height: 667 });

    // 模拟积分不足
    await page.route('/api/get-user-credits', async route => {
      await route.fulfill({
        json: {
          code: 0,
          data: { left_credits: 0 }
        }
      });
    });

    await page.reload();
    await page.waitForLoadState('networkidle');

    // 验证移动端布局
    await expect(page.locator('text=0')).toBeVisible();
    await expect(page.locator('text=⚠ Need 2 more credits')).toBeVisible();

    // 验证按钮在移动端的显示
    const getCreditsButton = page.getByRole('button', { name: /get credits/i });
    await expect(getCreditsButton).toBeVisible();

    // 验证按钮可点击
    await expect(getCreditsButton).toBeEnabled();
  });
});
