{"buildCommand": "pnpm build", "devCommand": "pnpm dev", "installCommand": "pnpm install", "framework": "nextjs", "regions": ["iad1"], "functions": {"src/app/api/**/*.ts": {"maxDuration": 60}, "app/api/**/*": {"maxDuration": 60}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}]}