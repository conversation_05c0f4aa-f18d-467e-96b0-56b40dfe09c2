import 'dotenv/config';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.development' });
config({ path: '.env.local' });
config({ path: '.env' });

async function testCreditsAPI() {
  try {
    console.log('Testing credits API...');
    
    // Test the API endpoint directly
    const response = await fetch('http://localhost:3000/api/user/credits', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // Note: This won't work without proper session, but let's see the response
      }
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const data = await response.json();
    console.log('Response data:', data);
    
  } catch (error) {
    console.error('❌ Error testing credits API:', error);
    console.error('Error details:', error instanceof Error ? error.message : String(error));
  }
  
  process.exit(0);
}

testCreditsAPI();
