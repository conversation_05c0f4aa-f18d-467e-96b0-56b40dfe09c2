# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

LoopCraft is an AI-powered seamless loop music generation platform built with Next.js 15, TypeScript, and Drizzle ORM. The platform allows users to generate royalty-free background music for content creation with features like watermarking, loop verification, and subscription tiers.

## Key Architecture

- **Frontend**: Next.js 15 App Router with TypeScript
- **Database**: PostgreSQL with Drizzle ORM
- **Authentication**: NextAuth.js (v5 beta)
- **Internationalization**: next-intl for i18n
- **Styling**: Tailwind CSS with shadcn/ui components
- **Music Generation**: Multi-provider support (Mubert, Suno, Volcengine)
- **File Storage**: AWS S3 with presigned URLs
- **Payments**: Stripe integration
- **Deployment**: Vercel (primary), Cloudflare Pages (alternative)

## Core Services

### Music Generation Pipeline
- `src/services/music-generation.ts` - Main orchestration service
- `src/services/music-provider.ts` - Provider abstraction layer
- `src/services/watermark-service.ts` - Audio watermarking for free users
- `src/services/audio-analysis.ts` - Loop detection and verification
- `src/services/stem-generation.ts` - Track separation service

### Database Models
- **users** - User accounts with subscription tiers
- **music_generations** - Generation requests and status tracking
- **tracks** - Generated music metadata and files
- **orders** - Stripe payment records
- **credits** - Credit system for usage tracking

## Development Commands

```bash
# Development
pnpm dev                 # Start development server with turbopack
pnpm build              # Build for production
pnpm start              # Start production server

# Testing
pnpm test               # Run Jest tests
pnpm test:watch         # Run tests in watch mode
pnpm test:coverage      # Generate coverage report

# Database
pnpm db:generate        # Generate migrations from schema
pnpm db:migrate         # Apply migrations to database
pnpm db:studio          # Open Drizzle Studio
pnpm db:push            # Push schema changes to database

# Analysis
pnpm analyze            # Bundle analysis with @next/bundle-analyzer

# Docker
pnpm docker:build       # Build Docker image
```

## Key File Structure

```
src/
├── app/                    # Next.js App Router
│   ├── [locale]/          # i18n routes
│   │   ├── (default)/     # Main application
│   │   └── (admin)/       # Admin dashboard
│   └── api/               # API routes
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   ├── music/            # Music-specific components
│   └── blocks/           # Landing page sections
├── services/             # Business logic
├── models/               # Database queries
├── db/                   # Drizzle schema and config
├── types/                # TypeScript definitions
└── lib/                  # Utilities and helpers
```

## Environment Setup

Key environment variables needed:
- `DATABASE_URL` - PostgreSQL connection string
- `NEXTAUTH_SECRET` - NextAuth.js secret
- `NEXTAUTH_URL` - Application URL
- `STRIPE_SECRET_KEY` - Stripe payments
- `AWS_ACCESS_KEY_ID` - S3 storage
- `AWS_SECRET_ACCESS_KEY` - S3 storage
- `AWS_S3_BUCKET` - S3 bucket name

## Provider Integration

### Music Generation Providers
- **Mubert**: Primary provider for quick generation
- **Suno**: High-quality generation with longer processing
- **Volcengine**: Chinese market provider (recently removed)

### Payment Tiers
- **Free**: 3 generations/month, watermarked audio
- **Professional**: 20 generations/month, watermark-free
- **Commercial**: Unlimited generations, commercial license

## Testing Strategy

- Unit tests with Jest and React Testing Library
- Integration tests for API routes
- Database tests with test database
- E2E tests for critical user flows

## Code Patterns

- Service layer pattern for business logic
- Repository pattern for database access
- Provider abstraction for external services
- Type-safe API routes with Zod validation
- Error handling with proper HTTP status codes