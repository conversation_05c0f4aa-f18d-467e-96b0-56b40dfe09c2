# Volcano API vs 我们的实现 - 参数对比

## 1. Genre 参数对比

### API文档中的Genre列表：
```
["corporate","dance/edm","orchestral","chill out","rock","hip hop","folk","funk","ambient","holiday","jazz","kids","world","travel","commercial","advertising","driving","cinematic","upbeat","epic","inspiring","business","video game","dark","pop","trailer","modern","electronic","documentary","soundtrack","fashion","acoustic","movie","tv","high tech","industrial","dance","video","vlog","marketing","game","radio","promotional","sports","party","summer","beauty"]
```

### 我们的MUSIC_GENRES：
需要检查是否完全匹配

## 2. Mood 参数对比

### API文档中的Mood列表：
```
["positive","uplifting","energetic","happy","bright","optimistic","hopeful","cool","dreamy","fun","light","powerful","calm","confident","joyful","dramatic","peaceful","playful","soft","groovy","reflective","easy","relaxed","lively","smooth","romantic","intense","elegant","mellow","emotional","sentimental","cheerful happy","contemplative","soothing","proud","passionate","sweet","mystical","tranquil","cheerful","casual","beautiful","ethereal","melancholy","sad","aggressive","haunting","adventure","serene","sincere","funky","funny"]
```

### 我们的MUSIC_MOODS：
需要检查是否完全匹配

## 3. Instrument 参数对比

### API文档中的Instrument列表：
```
["piano","drums","guitar","percussion","synth","electric guitar","acoustic guitar","bass guitar","brass","violin","cello","flute","organ","trumpet","ukulele","saxophone","double bass","harp","glockenspiel","synthesizer","keyboard","marimba","bass","banjo","strings"]
```

### 我们的MUSIC_INSTRUMENTS：
需要检查是否完全匹配

## 4. Theme 参数对比

### API文档中的Theme列表：
```
["inspirational","motivational","achievement","discovery","every day","love","technology","lifestyle","journey","meditation","drama","children","hope","fantasy","holiday","health","family","real estate","media","kids","science","education","progress","world","vacation","training","christmas","sales"]
```

### 我们的MUSIC_THEMES：
需要检查是否完全匹配

## 5. 其他参数

### Duration
- API: Int类型，支持的值需要确认
- 我们的实现: [15, 30, 60]

### Text (Prompt)
- API: String，必填
- 我们的实现: ✅ 正确

## 检查结果

需要逐一比对每个数组，确保：
1. 所有API支持的值都在我们的常量中
2. 我们的常量中没有API不支持的值
3. 顺序和拼写完全一致
