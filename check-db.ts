import 'dotenv/config';
import { config } from 'dotenv';
import { db } from './src/db/index';
import { sql } from 'drizzle-orm';

// Load environment variables
config({ path: '.env.development' });
config({ path: '.env.local' });
config({ path: '.env' });

async function checkTable() {
  try {
    // Check public.users table (our application table)
    const publicUsersResult = await db().execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns
      WHERE table_name = 'users' AND table_schema = 'public'
      ORDER BY ordinal_position;
    `);
    console.log('Public.users table columns:');
    console.table(publicUsersResult);

    // Check if subscription_plan column exists in public.users
    const subscriptionPlanColumn = publicUsersResult.find(row => row.column_name === 'subscription_plan');
    if (subscriptionPlanColumn) {
      console.log('\n✅ subscription_plan column exists in public.users!');
      console.log('Column details:', subscriptionPlanColumn);
    } else {
      console.log('\n❌ subscription_plan column NOT found in public.users!');
    }

    // Also check what tables exist in public schema
    const tablesResult = await db().execute(sql`
      SELECT table_name
      FROM information_schema.tables
      WHERE table_schema = 'public'
      ORDER BY table_name;
    `);
    console.log('\nTables in public schema:');
    console.table(tablesResult);

  } catch (error) {
    console.error('Error:', error instanceof Error ? error.message : String(error));
  }
  process.exit(0);
}

checkTable();
