// Quick script to check the latest track record
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { tracks } = require('./src/db/schema.ts');
const { desc } = require('drizzle-orm');

async function checkLatestTrack() {
  try {
    // Create database connection
    const sql = postgres(process.env.DATABASE_URL || 'postgresql://localhost:5432/your_db');
    const db = drizzle(sql);

    // Get the latest track
    const latestTrack = await db
      .select()
      .from(tracks)
      .orderBy(desc(tracks.created_at))
      .limit(1);

    if (latestTrack.length > 0) {
      const track = latestTrack[0];
      console.log('\n=== Latest Track Record ===');
      console.log('UUID:', track.uuid);
      console.log('Title:', track.title?.substring(0, 50) + '...');
      console.log('Prompt:', track.prompt?.substring(0, 50) + '...');
      console.log('Style:', track.style);
      console.log('Mood:', track.mood);
      console.log('BPM:', track.bpm);
      console.log('Genre (NEW):', track.genre);
      console.log('Instrument (NEW):', track.instrument);
      console.log('Theme (NEW):', track.theme);
      console.log('Created:', track.created_at);

      // Check if new fields are populated
      const hasNewFields = track.genre || track.instrument || track.theme;
      console.log('\n=== Fix Status ===');
      if (hasNewFields) {
        console.log('✅ SUCCESS: New fields are populated!');
        console.log('   Genre:', JSON.stringify(track.genre));
        console.log('   Instrument:', JSON.stringify(track.instrument));
        console.log('   Theme:', JSON.stringify(track.theme));
      } else {
        console.log('❌ New fields are still null');
      }

      // Parse metadata to compare
      if (track.metadata) {
        try {
          const metadata = JSON.parse(track.metadata);
          console.log('\n=== Metadata Comparison ===');
          console.log('Metadata genre:', metadata.genre);
          console.log('Metadata instrument:', metadata.instrument);
          console.log('Metadata theme:', metadata.theme);
        } catch (e) {
          console.log('Could not parse metadata');
        }
      }
    } else {
      console.log('No tracks found');
    }

    await sql.end();
  } catch (error) {
    console.error('Error:', error.message);
  }
}

checkLatestTrack();
