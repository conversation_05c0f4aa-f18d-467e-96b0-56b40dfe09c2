// Test script for music generation API with new parameters
const testMusicGeneration = async () => {
  const testData = {
    prompt: "Upbeat electronic music for a tech startup video",
    duration: 30,
    bpm: 120,
    genres: ["electronic", "corporate", "upbeat"],
    instruments: ["synth", "drums", "bass"],
    themes: ["technology", "business", "motivational"]
  };

  try {
    console.log("Testing music generation API with new parameters...");
    console.log("Test data:", JSON.stringify(testData, null, 2));

    const response = await fetch("http://localhost:3001/api/music/generate", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Note: In real test, you'd need proper authentication headers
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    console.log("API Response:", JSON.stringify(result, null, 2));

    if (response.ok) {
      console.log("✅ API call successful!");
      return result;
    } else {
      console.log("❌ API call failed:", result.message);
      return null;
    }
  } catch (error) {
    console.error("❌ Error testing API:", error);
    return null;
  }
};

// Test the API
testMusicGeneration();
