# LoopCraft

AI-powered seamless loop music generation platform for creators and content makers.

![preview](preview.png)

## Quick Start

1. Clone the repository

```bash
git clone https://github.com/opcraft/loopcraft.git
```

2. Install dependencies

```bash
pnpm install
```

3. Run the development server

```bash
pnpm dev
```

## Customize

- Set your environment variables

```bash
cp .env.example .env.development
```

- Set your theme in `src/app/theme.css`

[tweakcn](https://tweakcn.com/editor/theme)

- Set your landing page content in `src/i18n/pages/landing`

- Set your i18n messages in `src/i18n/messages`

## Deploy

- Deploy to Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fopcraft%2Fopcraft-ai&project-name=my-opcraft-project&repository-name=my-opcraft-project&redirect-url=https%3A%2F%2Fopcraft.app&demo-title=LoopCraft&demo-description=AI-powered%20loop%20background%20music%20generation%20platform&demo-url=https%3A%2F%2Fopcraft.app)

- Deploy to Cloudflare

for new project, clone with branch "cloudflare"

```shell
git clone -b cloudflare https://github.com/opcraft/opcraft-ai.git
```

for exist project, checkout to branch "cloudflare"

```shell
git checkout cloudflare
```

1. Customize your environment variables

```bash
cp .env.example .env.production
cp wrangler.toml.example wrangler.toml
```

edit your environment variables in `.env.production`

and put all the environment variables under `[vars]` in `wrangler.toml`

2. Deploy

```bash
npm run cf:deploy
```

## Community

- [LoopCraft](https://loopcraft.app)
- [Documentation](https://docs.opcraft.app)

## License

- [LoopCraft AI Music Generation Platform License Agreement](LICENSE)
