// Test script to verify database field fix
const { Client } = require('pg');

async function testDatabaseFix() {
  // Database connection (adjust as needed)
  const client = new Client({
    connectionString: process.env.DATABASE_URL || 'postgresql://localhost:5432/your_db'
  });

  try {
    await client.connect();
    console.log('Connected to database');

    // Check the latest track record
    const result = await client.query(`
      SELECT 
        uuid, 
        prompt, 
        style, 
        mood, 
        bpm, 
        genre, 
        instrument, 
        theme, 
        metadata,
        created_at
      FROM tracks 
      ORDER BY created_at DESC 
      LIMIT 1
    `);

    if (result.rows.length > 0) {
      const track = result.rows[0];
      console.log('\n=== Latest Track Record ===');
      console.log('UUID:', track.uuid);
      console.log('Prompt:', track.prompt?.substring(0, 50) + '...');
      console.log('Style:', track.style);
      console.log('Mood:', track.mood);
      console.log('BPM:', track.bpm);
      console.log('Genre (new field):', track.genre);
      console.log('Instrument (new field):', track.instrument);
      console.log('Theme (new field):', track.theme);
      
      // Parse metadata to see if data is there
      if (track.metadata) {
        try {
          const metadata = JSON.parse(track.metadata);
          console.log('\nMetadata contains:');
          console.log('- Genre in metadata:', metadata.genre);
          console.log('- Instrument in metadata:', metadata.instrument);
          console.log('- Theme in metadata:', metadata.theme);
        } catch (e) {
          console.log('Could not parse metadata');
        }
      }

      // Check if the fix worked
      const hasNewFields = track.genre || track.instrument || track.theme;
      console.log('\n=== Fix Status ===');
      if (hasNewFields) {
        console.log('✅ SUCCESS: New fields are being populated!');
      } else {
        console.log('❌ ISSUE: New fields are still null');
        console.log('   This means the track was created before the fix');
        console.log('   Try generating a new track to test the fix');
      }
    } else {
      console.log('No tracks found in database');
    }

  } catch (error) {
    console.error('Database error:', error.message);
  } finally {
    await client.end();
  }
}

// Run the test
testDatabaseFix();
