// Quick script to check track file URL
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { tracks } = require('./src/db/schema.ts');
const { desc, like } = require('drizzle-orm');

async function checkTrackUrl() {
  try {
    // Create database connection
    const sql = postgres(process.env.DATABASE_URL || 'postgresql://localhost:5432/your_db');
    const db = drizzle(sql);

    // Get tracks with UUID starting with 9dd8cc56
    const matchingTracks = await db
      .select()
      .from(tracks)
      .where(like(tracks.uuid, '9dd8cc56%'))
      .orderBy(desc(tracks.created_at))
      .limit(5);

    if (matchingTracks.length > 0) {
      console.log('\n=== Matching Track Records ===');
      matchingTracks.forEach((track, index) => {
        console.log(`\n--- Track ${index + 1} ---`);
        console.log('UUID:', track.uuid);
        console.log('Title:', track.title);
        console.log('Prompt:', track.prompt);
        console.log('File URL:', track.file_url);
        console.log('File Size:', track.file_size);
        console.log('File Format:', track.file_format);
        console.log('Original File URL:', track.original_file_url);
        console.log('Created:', track.created_at);
      });
    } else {
      console.log('No tracks found with UUID starting with 9dd8cc56');
      
      // Get the latest track instead
      const latestTrack = await db
        .select()
        .from(tracks)
        .orderBy(desc(tracks.created_at))
        .limit(1);

      if (latestTrack.length > 0) {
        const track = latestTrack[0];
        console.log('\n=== Latest Track Record ===');
        console.log('UUID:', track.uuid);
        console.log('Title:', track.title);
        console.log('Prompt:', track.prompt);
        console.log('File URL:', track.file_url);
        console.log('File Size:', track.file_size);
        console.log('File Format:', track.file_format);
        console.log('Original File URL:', track.original_file_url);
        console.log('Created:', track.created_at);
      }
    }

    await sql.end();
  } catch (error) {
    console.error('Error checking track URL:', error);
  }
}

checkTrackUrl();
