import 'dotenv/config';
import { config } from 'dotenv';

// Load environment variables
config({ path: '.env.development' });
config({ path: '.env.local' });
config({ path: '.env' });

console.log('Environment variables:');
console.log('DATABASE_URL:', process.env.DATABASE_URL ? 'SET' : 'NOT SET');
console.log('DATABASE_URL (first 50 chars):', process.env.DATABASE_URL?.substring(0, 50));
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('AUTH_SECRET:', process.env.AUTH_SECRET ? 'SET' : 'NOT SET');
console.log('AUTH_GOOGLE_ID:', process.env.AUTH_GOOGLE_ID ? 'SET' : 'NOT SET');

// Check if we're using the right database
if (process.env.DATABASE_URL) {
  const url = new URL(process.env.DATABASE_URL);
  console.log('Database host:', url.hostname);
  console.log('Database name:', url.pathname);
  console.log('Database port:', url.port);
}
