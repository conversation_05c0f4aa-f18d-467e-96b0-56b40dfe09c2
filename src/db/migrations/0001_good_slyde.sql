CREATE TABLE "collection_tracks" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "collection_tracks_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"collection_uuid" varchar(255) NOT NULL,
	"track_uuid" varchar(255) NOT NULL,
	"added_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "loop_verifications" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "loop_verifications_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"track_uuid" varchar(255) NOT NULL,
	"verification_score" numeric(3, 2),
	"is_seamless" boolean DEFAULT false NOT NULL,
	"start_analysis" json,
	"end_analysis" json,
	"verification_method" varchar(50) NOT NULL,
	"created_at" timestamp with time zone DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "music_generations" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "music_generations_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"uuid" varchar(255) NOT NULL,
	"user_uuid" varchar(255) NOT NULL,
	"prompt" text NOT NULL,
	"style" varchar(100),
	"mood" varchar(100),
	"bpm" integer,
	"duration" integer NOT NULL,
	"provider" varchar(50) NOT NULL,
	"provider_task_id" varchar(255),
	"status" varchar(50) DEFAULT 'pending' NOT NULL,
	"error_message" text,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone,
	"completed_at" timestamp with time zone,
	CONSTRAINT "music_generations_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE "track_stems" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "track_stems_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"uuid" varchar(255) NOT NULL,
	"track_uuid" varchar(255) NOT NULL,
	"stem_type" varchar(50) NOT NULL,
	"file_url" varchar(500) NOT NULL,
	"file_size" integer,
	"file_format" varchar(10) DEFAULT 'wav' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "track_stems_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE "track_variations" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "track_variations_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"uuid" varchar(255) NOT NULL,
	"original_track_uuid" varchar(255) NOT NULL,
	"user_uuid" varchar(255) NOT NULL,
	"variation_type" varchar(50) NOT NULL,
	"variation_params" json,
	"file_url" varchar(500) NOT NULL,
	"file_size" integer,
	"file_format" varchar(10) DEFAULT 'mp3' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	CONSTRAINT "track_variations_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
CREATE TABLE "tracks" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "tracks_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"uuid" varchar(255) NOT NULL,
	"generation_uuid" varchar(255) NOT NULL,
	"user_uuid" varchar(255) NOT NULL,
	"title" varchar(255),
	"slug" varchar(255),
	"prompt" text NOT NULL,
	"style" varchar(100),
	"mood" varchar(100),
	"bpm" integer,
	"duration" integer NOT NULL,
	"key_signature" varchar(10),
	"file_url" varchar(500) NOT NULL,
	"file_size" integer,
	"file_format" varchar(10) DEFAULT 'mp3' NOT NULL,
	"waveform_data" json,
	"metadata" json,
	"download_count" integer DEFAULT 0 NOT NULL,
	"is_public" boolean DEFAULT false NOT NULL,
	"is_premium" boolean DEFAULT false NOT NULL,
	"has_watermark" boolean DEFAULT true NOT NULL,
	"original_file_url" varchar(500),
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone,
	CONSTRAINT "tracks_uuid_unique" UNIQUE("uuid"),
	CONSTRAINT "tracks_slug_unique" UNIQUE("slug")
);
--> statement-breakpoint
CREATE TABLE "user_track_collections" (
	"id" integer PRIMARY KEY GENERATED ALWAYS AS IDENTITY (sequence name "user_track_collections_id_seq" INCREMENT BY 1 MINVALUE 1 MAXVALUE ********** START WITH 1 CACHE 1),
	"uuid" varchar(255) NOT NULL,
	"user_uuid" varchar(255) NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"is_public" boolean DEFAULT false NOT NULL,
	"created_at" timestamp with time zone DEFAULT now(),
	"updated_at" timestamp with time zone,
	CONSTRAINT "user_track_collections_uuid_unique" UNIQUE("uuid")
);
--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "subscription_plan" varchar(50) DEFAULT 'free';--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "subscription_status" varchar(50) DEFAULT 'inactive';--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "subscription_expires_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "subscription_stripe_id" varchar(255);--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "credits" integer DEFAULT 0 NOT NULL;