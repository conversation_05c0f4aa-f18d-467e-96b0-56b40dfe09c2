import { BaseMusicProvider } from "./base-music-provider";
import { MusicGenerationOptions, MusicProvider } from "@/types/music";

/**
 * 模拟音乐提供商 - 用于测试和开发
 */
export class MockProvider extends BaseMusicProvider {
  constructor(config: any) {
    super("mock", config);
  }

  getProviderInfo(): MusicProvider {
    return {
      name: "mock",
      display_name: "Mock Provider",
      supported_durations: [15, 30, 60],
      supported_styles: [
        "ambient", "corporate", "electronic", "acoustic", "cinematic",
        "upbeat", "chill", "jazz", "classical", "rock", "pop"
      ],
      max_bpm: this.getMaxBpm(),
      min_bpm: this.getMinBpm(),
      supports_stems: this.supportsStems(),
      supports_variations: this.supportsVariations()
    };
  }

  async generateMusic(
    prompt: string,
    duration: number,
    options?: MusicGenerationOptions
  ): Promise<{ task_id: string; estimated_time: number }> {
    console.log("Mock provider generating music:", {
      prompt,
      duration,
      options
    });

    // 模拟生成过程
    const taskId = `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // 模拟异步生成
    setTimeout(async () => {
      try {
        // 模拟生成完成，创建一个简单的音频文件 URL
        const audioUrl = `https://example.com/mock-audio/${taskId}.wav`;
        
        // 更新数据库状态为完成
        const { updateMusicGenerationStatus } = await import("@/models/music-generation");
        await updateMusicGenerationStatus(taskId, "completed");

        console.log(`Mock generation completed: ${taskId}`);
      } catch (error) {
        console.error(`Mock generation failed: ${taskId}`, error);
        
        // 更新状态为失败
        const { updateMusicGenerationStatus } = await import("@/models/music-generation");
        await updateMusicGenerationStatus(taskId, "failed", "Mock generation failed");
      }
    }, 5000); // 5秒后完成

    return {
      task_id: taskId,
      estimated_time: 5 // 5秒
    };
  }

  async checkStatus(taskId: string): Promise<any> {
    console.log("Mock provider checking status:", taskId);
    
    // 模拟状态检查
    return {
      status: "pending",
      progress: Math.floor(Math.random() * 100),
      message: "Mock generation in progress"
    };
  }

  async getResult(taskId: string): Promise<any> {
    console.log("Mock provider getting result:", taskId);
    
    // 模拟结果获取
    return {
      status: "completed",
      audio_url: `https://example.com/mock-audio/${taskId}.wav`,
      duration: 30,
      file_size: 1024 * 1024
    };
  }

  isConfigured(): boolean {
    // Mock 提供商总是可用的
    return true;
  }

  getDisplayName(): string {
    return "Mock Provider";
  }

  getSupportedDurations(): number[] {
    return [15, 30, 60];
  }

  getSupportedStyles(): string[] {
    return [
      "ambient", "corporate", "electronic", "acoustic", "cinematic",
      "upbeat", "chill", "jazz", "classical", "rock", "pop"
    ];
  }

  getSupportedMoods(): string[] {
    return [
      "peaceful", "energetic", "happy", "calm", "dramatic",
      "romantic", "mysterious", "uplifting", "relaxed", "powerful"
    ];
  }

  async cancelGeneration(task_id: string): Promise<boolean> {
    console.log("Mock provider cancelling generation:", task_id);
    return true;
  }

  protected getMaxBpm(): number {
    return 200;
  }

  protected getMinBpm(): number {
    return 60;
  }

  protected supportsStems(): boolean {
    return false;
  }

  protected supportsVariations(): boolean {
    return false;
  }
}
