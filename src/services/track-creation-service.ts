/**
 * Track Creation Service - 统一的音轨创建服务
 * 确保正确处理用户订阅状态、水印和premium字段
 */

import { insertTrack } from "@/models/track";
import { findUserByUuid } from "@/models/user";
import { WatermarkService } from "@/services/watermark-service";
import { generateTrackSlug } from "@/lib/track-slug";
import { getUuid } from "@/lib/hash";
import type { MusicGeneration } from "@/types/music";

interface TrackCreationOptions {
  generation: any; // 使用any来兼容不同的generation类型
  file_url: string;
  file_size?: number;
  file_format?: string;
  metadata?: any;
  title?: string;
  is_public?: boolean;
}

interface UserSubscription {
  plan: "free" | "professional" | "commercial";
  is_active: boolean;
  expires_at?: string;
}

export class TrackCreationService {
  
  /**
   * 创建音轨记录，正确处理用户订阅状态和水印
   */
  static async createTrack(options: TrackCreationOptions) {
    const {
      generation,
      file_url,
      file_size = 0,
      file_format = "mp3",
      metadata = {},
      title,
      is_public = true
    } = options;

    try {
      // 1. 获取用户信息
      const user = await findUserByUuid(generation.user_uuid);
      if (!user) {
        throw new Error(`User not found: ${generation.user_uuid}`);
      }

      // 2. 确定用户订阅状态
      const userSubscription: UserSubscription = {
        plan: (user.subscription_plan as "free" | "professional" | "commercial") || "free",
        is_active: user.subscription_status === "active",
        expires_at: user.subscription_expires_at?.toISOString(),
      };

      console.log("User subscription status:", {
        user_uuid: generation.user_uuid,
        plan: userSubscription.plan,
        is_active: userSubscription.is_active,
        expires_at: userSubscription.expires_at,
      });

      // 3. 生成track UUID和slug
      const trackUuid = getUuid();
      const slug = generateTrackSlug({
        prompt: generation.prompt,
        bpm: generation.bpm,
        uuid: trackUuid,
        style: generation.style
      });

      // 4. 处理音频文件（水印）
      const processedAudio = await WatermarkService.processAudioForUser(
        file_url,
        userSubscription,
        generation.duration,
        trackUuid
      );

      console.log("Audio processing result:", {
        track_uuid: trackUuid,
        original_url: file_url,
        processed_url: processedAudio.processed_url,
        has_watermark: processedAudio.has_watermark,
        is_premium: !processedAudio.has_watermark,
        processing_time: processedAudio.processing_time,
      });

      // 5. 创建track记录
      const trackData = {
        uuid: trackUuid,
        user_uuid: generation.user_uuid,
        generation_uuid: generation.uuid,
        title: title || generation.prompt.substring(0, 100),
        slug: slug,
        prompt: generation.prompt,
        style: generation.style || undefined,
        mood: generation.mood || undefined,
        bpm: generation.bpm || undefined,
        duration: generation.duration,
        // New fields for API compatibility
        genre: generation.genre,
        instrument: generation.instrument,
        theme: generation.theme,
        // File information
        file_url: processedAudio.processed_url,
        file_size: file_size,
        file_format: file_format,
        metadata: JSON.stringify(metadata),
        // Premium and watermark fields - 关键部分！
        is_premium: !processedAudio.has_watermark,
        has_watermark: processedAudio.has_watermark,
        original_file_url: processedAudio.has_watermark ? file_url : null,
        // Other fields
        download_count: 0,
        is_public: is_public,
        created_at: new Date(),
        updated_at: new Date(),
      };

      const track = await insertTrack(trackData);

      console.log("Track created successfully:", {
        track_uuid: trackUuid,
        generation_uuid: generation.uuid,
        user_plan: userSubscription.plan,
        is_premium: !processedAudio.has_watermark,
        has_watermark: processedAudio.has_watermark,
      });

      return track;

    } catch (error) {
      console.error("Track creation failed:", error);
      throw error;
    }
  }

  /**
   * 获取用户订阅状态
   */
  static async getUserSubscription(user_uuid: string): Promise<UserSubscription> {
    const user = await findUserByUuid(user_uuid);
    if (!user) {
      throw new Error(`User not found: ${user_uuid}`);
    }

    return {
      plan: (user.subscription_plan as "free" | "professional" | "commercial") || "free",
      is_active: user.subscription_status === "active",
      expires_at: user.subscription_expires_at?.toISOString(),
    };
  }

  /**
   * 检查用户是否为premium用户
   */
  static async isUserPremium(user_uuid: string): Promise<boolean> {
    try {
      const subscription = await this.getUserSubscription(user_uuid);
      
      if (subscription.plan === "free") {
        return false;
      }

      if (!subscription.is_active) {
        return false;
      }

      if (subscription.expires_at) {
        const expiryDate = new Date(subscription.expires_at);
        if (expiryDate < new Date()) {
          return false;
        }
      }

      return true;
    } catch (error) {
      console.error("Failed to check user premium status:", error);
      return false;
    }
  }

  /**
   * 为现有track升级到premium版本（用户升级订阅后）
   */
  static async upgradeTrackToPremium(track_uuid: string): Promise<boolean> {
    try {
      // TODO: 实现track升级逻辑
      // 1. 获取track信息
      // 2. 检查是否有original_file_url
      // 3. 移除水印，更新文件URL
      // 4. 更新is_premium和has_watermark字段
      
      console.log("Track upgrade to premium:", track_uuid);
      return true;
    } catch (error) {
      console.error("Failed to upgrade track to premium:", error);
      return false;
    }
  }
}
