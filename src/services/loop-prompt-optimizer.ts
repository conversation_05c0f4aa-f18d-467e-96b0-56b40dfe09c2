/**
 * Loop Prompt Optimizer - 循环提示词优化器
 * 专门用于优化音乐生成提示词，提高循环质量
 */

export interface LoopOptimizationOptions {
  duration: 15 | 30 | 60;
  style?: string;
  mood?: string;
  bpm?: number;
  provider: "volcano" | "mubert" | "suno";
}

export class LoopPromptOptimizer {
  
  /**
   * 优化用户提示词，添加循环质量指令
   */
  static optimizePrompt(
    originalPrompt: string, 
    options: LoopOptimizationOptions
  ): string {
    const { duration, style, mood, bpm, provider } = options;
    
    // 基础循环指令
    const baseLoopInstructions = this.getBaseLoopInstructions(provider);
    
    // 根据时长调整循环策略
    const durationSpecificInstructions = this.getDurationSpecificInstructions(duration);
    
    // 根据风格调整循环策略
    const styleSpecificInstructions = this.getStyleSpecificInstructions(style);
    
    // 根据BPM调整循环策略
    const bpmSpecificInstructions = this.getBpmSpecificInstructions(bpm);
    
    // 组合优化后的提示词
    const optimizedPrompt = [
      originalPrompt,
      baseLoopInstructions,
      durationSpecificInstructions,
      styleSpecificInstructions,
      bpmSpecificInstructions,
    ].filter(Boolean).join('. ');
    
    return optimizedPrompt;
  }
  
  /**
   * 获取基础循环指令（针对不同提供商）
   */
  private static getBaseLoopInstructions(provider: string): string {
    const instructions = {
      volcano: "Create a seamless loop with smooth transitions. Ensure the ending flows naturally back to the beginning. Use consistent rhythm and harmony throughout. Avoid abrupt changes at the start or end.",
      
      mubert: "Generate a perfect loop with seamless transitions. The track should start and end with compatible audio characteristics. Maintain consistent energy and avoid sudden cuts or fades.",
      
      suno: "Make this a seamless loop that can repeat infinitely. Ensure the beginning and end have matching musical elements. Use consistent tempo and avoid jarring transitions.",
    };
    
    return instructions[provider as keyof typeof instructions] || instructions.suno;
  }
  
  /**
   * 获取时长特定的循环指令
   */
  private static getDurationSpecificInstructions(duration: number): string {
    const instructions = {
      15: "Create a tight, focused loop with immediate musical development. Ensure quick establishment of the main theme that loops perfectly.",
      
      30: "Develop a well-structured loop with clear musical phrases that cycle smoothly. Allow for musical development while maintaining loop integrity.",
      
      60: "Create an extended loop with multiple musical sections that flow together seamlessly. Ensure all sections work together for perfect looping.",
    };
    
    return instructions[duration as keyof typeof instructions] || "";
  }
  
  /**
   * 获取风格特定的循环指令
   */
  private static getStyleSpecificInstructions(style?: string): string {
    if (!style) return "";
    
    const styleInstructions: Record<string, string> = {
      "electronic": "Use consistent electronic elements and avoid filter sweeps at loop points. Maintain steady electronic rhythm.",
      
      "ambient": "Create smooth ambient textures that blend seamlessly. Use gradual changes and avoid sudden ambient shifts.",
      
      "corporate": "Maintain professional, consistent energy throughout. Ensure smooth business-appropriate transitions.",
      
      "cinematic": "Create dramatic but loopable cinematic elements. Avoid climactic endings that don't loop well.",
      
      "rock": "Maintain consistent rock energy and rhythm. Ensure guitar and drum patterns loop naturally.",
      
      "jazz": "Create jazz progressions that resolve back to the beginning. Maintain swing and rhythm consistency.",
      
      "classical": "Use classical structures that support looping. Ensure harmonic progressions resolve appropriately.",
      
      "pop": "Create catchy pop elements that repeat naturally. Maintain consistent pop energy and structure.",
    };
    
    const normalizedStyle = style.toLowerCase();
    return styleInstructions[normalizedStyle] || "";
  }
  
  /**
   * 获取BPM特定的循环指令
   */
  private static getBpmSpecificInstructions(bpm?: number): string {
    if (!bpm) return "Maintain consistent tempo throughout for perfect looping.";
    
    if (bpm < 80) {
      return "Maintain slow, steady rhythm with consistent timing for seamless looping.";
    } else if (bpm < 120) {
      return "Keep moderate tempo consistent throughout with steady rhythm for perfect loops.";
    } else if (bpm < 140) {
      return "Maintain energetic but consistent rhythm. Ensure beat patterns loop seamlessly.";
    } else {
      return "Keep high-energy rhythm consistent. Ensure fast beats maintain perfect timing for looping.";
    }
  }
  
  /**
   * 生成负面提示词（避免不良循环效果）
   */
  static generateNegativePrompt(options: LoopOptimizationOptions): string {
    const commonNegativeElements = [
      "abrupt ending",
      "fade out",
      "fade in", 
      "sudden stop",
      "jarring transition",
      "inconsistent tempo",
      "volume changes",
      "key changes at the end",
      "dramatic finale",
      "crescendo ending",
      "diminuendo ending",
      "silence at start",
      "silence at end",
      "audio gaps",
      "clicking sounds",
      "audio artifacts",
      "mismatched rhythm",
      "tempo drift",
    ];
    
    // 根据风格添加特定的负面元素
    const styleSpecificNegative = this.getStyleSpecificNegativePrompt(options.style);
    
    return [...commonNegativeElements, ...styleSpecificNegative].join(", ");
  }
  
  /**
   * 获取风格特定的负面提示词
   */
  private static getStyleSpecificNegativePrompt(style?: string): string[] {
    if (!style) return [];
    
    const styleNegatives: Record<string, string[]> = {
      "electronic": ["filter sweeps at loop points", "build-ups", "drops", "risers"],
      "ambient": ["sudden ambient changes", "harsh transitions", "abrupt texture changes"],
      "rock": ["guitar solos ending", "drum fills at end", "power chord endings"],
      "classical": ["unresolved harmonies", "incomplete phrases", "dramatic pauses"],
      "cinematic": ["climactic endings", "dramatic crescendos", "movie-style conclusions"],
    };
    
    const normalizedStyle = style.toLowerCase();
    return styleNegatives[normalizedStyle] || [];
  }
  
  /**
   * 验证提示词质量
   */
  static validatePrompt(prompt: string): {
    isValid: boolean;
    score: number;
    suggestions: string[];
  } {
    const suggestions: string[] = [];
    let score = 100;
    
    // 检查是否包含循环关键词
    const loopKeywords = ["loop", "seamless", "repeat", "cycle", "continuous"];
    const hasLoopKeywords = loopKeywords.some(keyword => 
      prompt.toLowerCase().includes(keyword)
    );
    
    if (!hasLoopKeywords) {
      score -= 20;
      suggestions.push("Consider adding loop-specific keywords like 'seamless' or 'loop'");
    }
    
    // 检查是否包含可能影响循环的词汇
    const problematicWords = ["ending", "finale", "conclusion", "fade", "stop"];
    const hasProblematicWords = problematicWords.some(word => 
      prompt.toLowerCase().includes(word)
    );
    
    if (hasProblematicWords) {
      score -= 30;
      suggestions.push("Remove words that suggest endings or conclusions");
    }
    
    // 检查提示词长度
    if (prompt.length < 50) {
      score -= 10;
      suggestions.push("Consider adding more descriptive details");
    }
    
    if (prompt.length > 500) {
      score -= 10;
      suggestions.push("Consider shortening the prompt for better processing");
    }
    
    return {
      isValid: score >= 70,
      score,
      suggestions,
    };
  }
}