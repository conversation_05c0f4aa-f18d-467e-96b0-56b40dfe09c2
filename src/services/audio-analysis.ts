import { AudioAnalysis, WaveformData } from "@/types/music";

// Audio analysis service for processing music files
export class AudioAnalysisService {
  
  // Analyze audio file for loop verification
  static async analyzeAudioLoop(
    file_url: string,
    method: "ml" | "signal_processing" = "ml"
  ): Promise<{
    is_seamless: boolean;
    verification_score: number;
    start_analysis: AudioAnalysis;
    end_analysis: AudioAnalysis;
  }> {
    try {
      console.log(`Analyzing audio loop: ${file_url} using ${method}`);

      // Download audio file
      const audioBuffer = await this.downloadAudioFile(file_url);
      const audioData = this.decodeAudioBuffer(audioBuffer);

      // Extract start and end segments for comparison
      const startSegment = this.extractSegment(audioData, 0, 2); // First 2 seconds
      const endSegment = this.extractSegment(audioData, -2, 0); // Last 2 seconds

      // Analyze both segments
      const start_analysis = await this.analyzeSegment(startSegment);
      const end_analysis = await this.analyzeSegment(endSegment);

      // Calculate similarity score
      const verification_score = this.calculateSimilarity(start_analysis, end_analysis, method);
      const is_seamless = verification_score > 0.8;

      return {
        is_seamless,
        verification_score,
        start_analysis,
        end_analysis,
      };
    } catch (error) {
      console.error("Audio loop analysis failed:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Audio analysis failed: ${errorMessage}`);
    }
  }

  // Generate waveform data for visualization
  static async generateWaveform(file_url: string): Promise<WaveformData> {
    try {
      console.log(`Generating waveform for: ${file_url}`);

      const audioBuffer = await this.downloadAudioFile(file_url);
      const audioData = this.decodeAudioBuffer(audioBuffer);

      // Generate peaks for waveform visualization
      const peaks = this.generatePeaks(audioData, 1000); // 1000 data points

      return {
        peaks,
        duration: audioData.duration,
        sample_rate: audioData.sample_rate,
      };
    } catch (error) {
      console.error("Waveform generation failed:", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new Error(`Waveform generation failed: ${errorMessage}`);
    }
  }

  // Analyze audio for BPM detection
  static async detectBPM(file_url: string): Promise<number> {
    try {
      console.log(`Detecting BPM for: ${file_url}`);

      const audioBuffer = await this.downloadAudioFile(file_url);
      const audioData = this.decodeAudioBuffer(audioBuffer);

      // Simple BPM detection algorithm
      const bpm = this.calculateBPM(audioData);
      
      return Math.round(bpm);
    } catch (error) {
      console.error("BPM detection failed:", error);
      return 120; // Default BPM
    }
  }

  // Analyze audio for key detection
  static async detectKey(file_url: string): Promise<string> {
    try {
      console.log(`Detecting key for: ${file_url}`);

      const audioBuffer = await this.downloadAudioFile(file_url);
      const audioData = this.decodeAudioBuffer(audioBuffer);

      // Simple key detection algorithm
      const key = this.calculateKey(audioData);
      
      return key;
    } catch (error) {
      console.error("Key detection failed:", error);
      return "C"; // Default key
    }
  }

  // Private helper methods

  private static async downloadAudioFile(url: string): Promise<ArrayBuffer> {
    const response = await fetch(url);
    if (!response.ok) {
      throw new Error(`Failed to download audio file: ${response.statusText}`);
    }
    return await response.arrayBuffer();
  }

  private static decodeAudioBuffer(buffer: ArrayBuffer): {
    data: Float32Array;
    duration: number;
    sample_rate: number;
    channels: number;
  } {
    // This is a simplified implementation
    // In a real application, you would use Web Audio API or a library like node-ffmpeg
    
    // Mock audio data for demonstration
    const sample_rate = 44100;
    const duration = 30; // 30 seconds
    const channels = 2;
    const samples = sample_rate * duration;
    
    // Generate mock audio data
    const data = new Float32Array(samples);
    for (let i = 0; i < samples; i++) {
      data[i] = Math.sin(2 * Math.PI * 440 * i / sample_rate) * 0.1; // 440Hz sine wave
    }

    return { data, duration, sample_rate, channels };
  }

  private static extractSegment(
    audioData: { data: Float32Array; sample_rate: number },
    startSeconds: number,
    endSeconds: number
  ): Float32Array {
    const { data, sample_rate } = audioData;
    const totalSamples = data.length;
    const totalDuration = totalSamples / sample_rate;

    let startSample = startSeconds >= 0 
      ? Math.floor(startSeconds * sample_rate)
      : Math.floor((totalDuration + startSeconds) * sample_rate);
    
    let endSample = endSeconds > 0
      ? Math.floor(endSeconds * sample_rate)
      : totalSamples + Math.floor(endSeconds * sample_rate);

    startSample = Math.max(0, Math.min(startSample, totalSamples));
    endSample = Math.max(startSample, Math.min(endSample, totalSamples));

    return data.slice(startSample, endSample);
  }

  private static async analyzeSegment(segment: Float32Array): Promise<AudioAnalysis> {
    // Calculate audio features
    const frequency_spectrum = this.calculateFFT(segment);
    const amplitude = this.calculateRMS(segment);
    const phase = this.calculatePhase(segment);
    const spectral_centroid = this.calculateSpectralCentroid(frequency_spectrum);
    const zero_crossing_rate = this.calculateZeroCrossingRate(segment);

    return {
      frequency_spectrum,
      amplitude,
      phase,
      spectral_centroid,
      zero_crossing_rate,
    };
  }

  private static calculateSimilarity(
    start: AudioAnalysis,
    end: AudioAnalysis,
    method: "ml" | "signal_processing"
  ): number {
    if (method === "ml") {
      // ML-based similarity (simplified)
      const spectralSimilarity = this.cosineSimilarity(start.frequency_spectrum, end.frequency_spectrum);
      const amplitudeSimilarity = 1 - Math.abs(start.amplitude - end.amplitude);
      const centroidSimilarity = 1 - Math.abs(start.spectral_centroid - end.spectral_centroid) / 5000;
      
      return (spectralSimilarity * 0.6 + amplitudeSimilarity * 0.2 + centroidSimilarity * 0.2);
    } else {
      // Signal processing similarity
      const spectralSimilarity = this.cosineSimilarity(start.frequency_spectrum, end.frequency_spectrum);
      const phaseSimilarity = 1 - Math.abs(start.phase - end.phase) / Math.PI;
      
      return (spectralSimilarity * 0.8 + phaseSimilarity * 0.2);
    }
  }

  private static generatePeaks(audioData: { data: Float32Array }, numPeaks: number): number[] {
    const { data } = audioData;
    const chunkSize = Math.floor(data.length / numPeaks);
    const peaks: number[] = [];

    for (let i = 0; i < numPeaks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, data.length);
      
      let max = 0;
      for (let j = start; j < end; j++) {
        max = Math.max(max, Math.abs(data[j]));
      }
      
      peaks.push(max);
    }

    return peaks;
  }

  private static calculateBPM(audioData: { data: Float32Array; sample_rate: number }): number {
    // Simplified BPM detection
    // In reality, this would involve onset detection and tempo analysis
    const { data, sample_rate } = audioData;
    
    // Mock BPM calculation
    const energy = this.calculateRMS(data);
    const estimatedBPM = 60 + (energy * 100); // Simple heuristic
    
    return Math.max(60, Math.min(200, estimatedBPM));
  }

  private static calculateKey(audioData: { data: Float32Array }): string {
    // Simplified key detection
    // In reality, this would involve chromagram analysis
    const keys = ["C", "C#", "D", "D#", "E", "F", "F#", "G", "G#", "A", "A#", "B"];

    // Use deterministic approach based on audio data to avoid hydration mismatch
    // Calculate a simple hash from the first few samples
    let hash = 0;
    const sampleCount = Math.min(100, audioData.data.length);
    for (let i = 0; i < sampleCount; i++) {
      hash += Math.abs(audioData.data[i]) * (i + 1);
    }

    const index = Math.floor(hash * 1000) % keys.length;
    return keys[index];
  }

  // Audio processing utility functions

  private static calculateFFT(data: Float32Array): number[] {
    // Simplified FFT - in reality, use a proper FFT library
    const spectrum: number[] = [];
    const N = Math.min(512, data.length);
    
    for (let k = 0; k < N / 2; k++) {
      let real = 0, imag = 0;
      for (let n = 0; n < N; n++) {
        const angle = -2 * Math.PI * k * n / N;
        real += data[n] * Math.cos(angle);
        imag += data[n] * Math.sin(angle);
      }
      spectrum.push(Math.sqrt(real * real + imag * imag));
    }
    
    return spectrum;
  }

  private static calculateRMS(data: Float32Array): number {
    let sum = 0;
    for (let i = 0; i < data.length; i++) {
      sum += data[i] * data[i];
    }
    return Math.sqrt(sum / data.length);
  }

  private static calculatePhase(data: Float32Array): number {
    // Simplified phase calculation
    let real = 0, imag = 0;
    for (let i = 0; i < Math.min(1024, data.length); i++) {
      const angle = 2 * Math.PI * i / data.length;
      real += data[i] * Math.cos(angle);
      imag += data[i] * Math.sin(angle);
    }
    return Math.atan2(imag, real);
  }

  private static calculateSpectralCentroid(spectrum: number[]): number {
    let weightedSum = 0, totalMagnitude = 0;
    
    for (let i = 0; i < spectrum.length; i++) {
      weightedSum += i * spectrum[i];
      totalMagnitude += spectrum[i];
    }
    
    return totalMagnitude > 0 ? (weightedSum / totalMagnitude) * 22050 / spectrum.length : 0;
  }

  private static calculateZeroCrossingRate(data: Float32Array): number {
    let crossings = 0;
    for (let i = 1; i < data.length; i++) {
      if ((data[i] >= 0) !== (data[i - 1] >= 0)) {
        crossings++;
      }
    }
    return crossings / data.length;
  }

  private static cosineSimilarity(a: number[], b: number[]): number {
    const minLength = Math.min(a.length, b.length);
    let dotProduct = 0, normA = 0, normB = 0;
    
    for (let i = 0; i < minLength; i++) {
      dotProduct += a[i] * b[i];
      normA += a[i] * a[i];
      normB += b[i] * b[i];
    }
    
    const magnitude = Math.sqrt(normA) * Math.sqrt(normB);
    return magnitude > 0 ? dotProduct / magnitude : 0;
  }
}
