/**
 * 音乐提供商基类
 */

import { Music<PERSON>rovider, MusicProviderConfig, MusicGenerationOptions } from "@/types/music";

// Abstract base class for music providers
export abstract class BaseMusicProvider {
  protected config: MusicProviderConfig;
  protected name: string;

  constructor(name: string, config: MusicProviderConfig) {
    this.name = name;
    this.config = config;
  }

  abstract generateMusic(
    prompt: string,
    duration: number,
    options?: MusicGenerationOptions
  ): Promise<{ task_id: string; estimated_time: number }>;

  abstract checkStatus(task_id: string): Promise<{
    status: "pending" | "processing" | "completed" | "failed";
    progress?: number;
    result?: { file_url: string; file_size: number; metadata?: any };
    error?: string;
  }>;

  abstract cancelGeneration(task_id: string): Promise<boolean>;

  // Abstract methods for provider information
  protected abstract getDisplayName(): string;
  protected abstract getSupportedDurations(): number[];
  protected abstract getSupportedStyles(): string[];
  protected abstract getMaxBpm(): number;
  protected abstract getMinBpm(): number;
  protected abstract supportsStems(): boolean;
  protected abstract supportsVariations(): boolean;

  // Get provider information
  getProviderInfo(): MusicProvider {
    return {
      name: this.name,
      display_name: this.getDisplayName(),
      supported_durations: this.getSupportedDurations(),
      supported_styles: this.getSupportedStyles(),
      max_bpm: this.getMaxBpm(),
      min_bpm: this.getMinBpm(),
      supports_stems: this.supportsStems(),
      supports_variations: this.supportsVariations(),
    };
  }

  // Validate generation request
  protected validateRequest(prompt: string, duration: number, options?: MusicGenerationOptions): void {
    if (!prompt || prompt.trim().length === 0) {
      throw new Error("Prompt is required and cannot be empty");
    }

    if (!this.getSupportedDurations().includes(duration)) {
      throw new Error(`Duration ${duration} is not supported. Supported durations: ${this.getSupportedDurations().join(", ")}`);
    }

    if (options?.bpm) {
      const bpm = options.bpm;
      if (bpm < this.getMinBpm() || bpm > this.getMaxBpm()) {
        throw new Error(`BPM ${bpm} is out of range. Supported range: ${this.getMinBpm()}-${this.getMaxBpm()}`);
      }
    }

    if (options?.style && !this.getSupportedStyles().includes(options.style)) {
      throw new Error(`Style "${options.style}" is not supported`);
    }
  }
}
