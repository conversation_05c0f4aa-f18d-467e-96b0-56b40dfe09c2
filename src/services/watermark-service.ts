interface WatermarkOptions {
  type: "audio" | "silent";
  position: "beginning" | "end" | "interval";
  interval?: number; // seconds between watermarks
  volume?: number; // 0.0 to 1.0
  duration?: number; // duration of watermark in seconds
}

interface UserSubscription {
  plan: "free" | "professional" | "commercial";
  is_active: boolean;
  expires_at?: string;
}

export class WatermarkService {
  
  /**
   * Determine if watermark should be applied based on user subscription
   */
  static shouldApplyWatermark(
    userSubscription: UserSubscription,
    trackDuration: number
  ): boolean {
    // Free users always get watermarked content
    if (userSubscription.plan === "free") {
      return true;
    }

    // Professional and Commercial users get watermark-free content
    if (userSubscription.plan === "professional" || userSubscription.plan === "commercial") {
      // Check if subscription is active
      if (!userSubscription.is_active) {
        return true; // Apply watermark if subscription expired
      }

      if (userSubscription.expires_at) {
        const expiryDate = new Date(userSubscription.expires_at);
        if (expiryDate < new Date()) {
          return true; // Apply watermark if subscription expired
        }
      }

      return false; // No watermark for active paid users
    }

    // Default to watermarked for unknown plans
    return true;
  }

  /**
   * Get watermark configuration based on user plan and track properties
   */
  static getWatermarkConfig(
    userSubscription: UserSubscription,
    trackDuration: number
  ): WatermarkOptions | null {
    if (!this.shouldApplyWatermark(userSubscription, trackDuration)) {
      return null;
    }

    // Different watermark strategies based on track duration
    if (trackDuration <= 15) {
      // Short tracks: single watermark at the beginning
      return {
        type: "audio",
        position: "beginning",
        volume: 0.3,
        duration: 2,
      };
    } else if (trackDuration <= 30) {
      // Medium tracks: watermark at beginning and end
      return {
        type: "audio",
        position: "beginning",
        volume: 0.3,
        duration: 2,
      };
    } else {
      // Long tracks: interval watermarks
      return {
        type: "audio",
        position: "interval",
        interval: 20, // Every 20 seconds
        volume: 0.25,
        duration: 1.5,
      };
    }
  }

  /**
   * Apply watermark to audio file (mock implementation)
   */
  static async applyWatermark(
    originalAudioUrl: string,
    watermarkConfig: WatermarkOptions,
    outputPath: string
  ): Promise<string> {
    console.log("Applying watermark:", {
      originalAudioUrl,
      watermarkConfig,
      outputPath,
    });

    // In a real implementation, this would:
    // 1. Download the original audio file
    // 2. Use FFmpeg or similar to overlay watermark
    // 3. Upload the watermarked file to storage
    // 4. Return the new URL

    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Mock watermarked URL
    const watermarkedUrl = originalAudioUrl.replace(
      /\.(mp3|wav|m4a)$/,
      `-watermarked.$1`
    );

    console.log("Watermark applied successfully:", watermarkedUrl);
    return watermarkedUrl;
  }

  /**
   * Generate watermark audio content
   */
  static async generateWatermarkAudio(): Promise<string> {
    // In a real implementation, this would generate or retrieve
    // a short audio clip saying "LoopCraft" or similar
    
    // For now, return a mock URL
    return "https://storage.example.com/watermarks/loopcraft-watermark.mp3";
  }

  /**
   * Remove watermark from audio (for upgrades)
   */
  static async removeWatermark(
    watermarkedAudioUrl: string,
    originalAudioUrl: string
  ): Promise<string> {
    console.log("Removing watermark:", {
      watermarkedAudioUrl,
      originalAudioUrl,
    });

    // In a real implementation, this would return the original
    // unwatermarked version if available, or process the audio
    // to remove watermarks (though this is technically challenging)

    return originalAudioUrl;
  }

  /**
   * Check if audio file has watermark
   */
  static isWatermarked(audioUrl: string): boolean {
    return audioUrl.includes("-watermarked");
  }

  /**
   * Get watermark information for display
   */
  static getWatermarkInfo(userSubscription: UserSubscription): {
    has_watermark: boolean;
    watermark_type: string;
    removal_available: boolean;
    upgrade_required: boolean;
  } {
    const hasWatermark = this.shouldApplyWatermark(userSubscription, 30);

    return {
      has_watermark: hasWatermark,
      watermark_type: hasWatermark ? "audio" : "none",
      removal_available: hasWatermark,
      upgrade_required: hasWatermark && userSubscription.plan === "free",
    };
  }

  /**
   * Process audio file with appropriate watermarking
   */
  static async processAudioForUser(
    originalAudioUrl: string,
    userSubscription: UserSubscription,
    trackDuration: number,
    trackUuid: string
  ): Promise<{
    processed_url: string;
    has_watermark: boolean;
    processing_time: number;
  }> {
    const startTime = Date.now();

    const watermarkConfig = this.getWatermarkConfig(userSubscription, trackDuration);

    if (!watermarkConfig) {
      // No watermark needed
      return {
        processed_url: originalAudioUrl,
        has_watermark: false,
        processing_time: Date.now() - startTime,
      };
    }

    // Apply watermark
    const outputPath = `processed/${trackUuid}-watermarked.mp3`;
    const processedUrl = await this.applyWatermark(
      originalAudioUrl,
      watermarkConfig,
      outputPath
    );

    return {
      processed_url: processedUrl,
      has_watermark: true,
      processing_time: Date.now() - startTime,
    };
  }

  /**
   * Get upgrade message for watermarked content
   */
  static getUpgradeMessage(userSubscription: UserSubscription): string {
    if (userSubscription.plan === "free") {
      return "Upgrade to Professional plan to remove watermarks and unlock high-quality downloads.";
    }

    if (!userSubscription.is_active) {
      return "Reactivate your subscription to access watermark-free downloads.";
    }

    return "";
  }

  /**
   * Validate watermark configuration
   */
  static validateWatermarkConfig(config: WatermarkOptions): {
    valid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    if (!["audio", "silent"].includes(config.type)) {
      errors.push("Invalid watermark type");
    }

    if (!["beginning", "end", "interval"].includes(config.position)) {
      errors.push("Invalid watermark position");
    }

    if (config.position === "interval" && (!config.interval || config.interval < 5)) {
      errors.push("Interval must be at least 5 seconds");
    }

    if (config.volume !== undefined && (config.volume < 0 || config.volume > 1)) {
      errors.push("Volume must be between 0.0 and 1.0");
    }

    if (config.duration !== undefined && (config.duration < 0.5 || config.duration > 5)) {
      errors.push("Duration must be between 0.5 and 5 seconds");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }

  /**
   * Get watermark statistics
   */
  static getWatermarkStats(): {
    total_watermarked: number;
    total_clean: number;
    watermark_types: Record<string, number>;
  } {
    // In a real implementation, this would query the database
    // For now, return mock data
    return {
      total_watermarked: 1250,
      total_clean: 890,
      watermark_types: {
        audio: 1100,
        silent: 150,
      },
    };
  }
}
