import {
  CreditsTransType,
  increaseCredits,
  updateCreditForOrder,
} from "./credit";
import {
  findOrderByOrderNo,
  OrderStatus,
  updateOrderStatus,
} from "@/models/order";
import { updateUserSubscription } from "@/models/user";
import { getIsoTimestr } from "@/lib/time";

import <PERSON><PERSON> from "stripe";
import { updateAffiliateForOrder } from "./affiliate";
import { Order } from "@/types/order";

export async function handleOrderSession(session: Stripe.Checkout.Session) {
  try {
    if (
      !session ||
      !session.metadata ||
      !session.metadata.order_no ||
      session.payment_status !== "paid"
    ) {
      throw new Error("invalid session");
    }

    const order_no = session.metadata.order_no;
    const paid_email =
      session.customer_details?.email || session.customer_email || "";
    const paid_detail = JSON.stringify(session);

    const order = await findOrderByOrderNo(order_no);
    if (!order || order.status !== OrderStatus.Created) {
      throw new Error("invalid order");
    }

    const paid_at = getIsoTimestr();
    await updateOrderStatus(
      order_no,
      OrderStatus.Paid,
      paid_at,
      paid_email,
      paid_detail
    );

    if (order.user_uuid) {
      if (order.credits > 0) {
        // increase credits for paied order
        await updateCreditForOrder(order as unknown as Order);
      }

      // update affiliate for paied order
      await updateAffiliateForOrder(order as unknown as Order);

      // update user subscription status for subscription orders
      await updateUserSubscriptionForOrder(order as unknown as Order);
    }

    console.log(
      "handle order session successed: ",
      order_no,
      paid_at,
      paid_email,
      paid_detail
    );
  } catch (e) {
    console.log("handle order session failed: ", e);
    throw e;
  }
}

/**
 * 根据订单更新用户订阅状态
 */
async function updateUserSubscriptionForOrder(order: Order) {
  try {
    // 检查是否为订阅订单
    if (!order.product_id || !order.valid_months || order.valid_months <= 0) {
      console.log("Not a subscription order, skipping subscription update:", {
        order_no: order.order_no,
        product_id: order.product_id,
        valid_months: order.valid_months,
      });
      return;
    }

    // 确定订阅计划
    let subscription_plan = "free";
    if (order.product_name?.toLowerCase().includes("professional")) {
      subscription_plan = "professional";
    } else if (order.product_name?.toLowerCase().includes("commercial")) {
      subscription_plan = "commercial";
    } else if (order.product_name?.toLowerCase().includes("pro")) {
      subscription_plan = "professional";
    }

    // 计算过期时间
    const now = new Date();
    const expires_at = new Date(now);
    expires_at.setMonth(expires_at.getMonth() + order.valid_months);

    // 更新用户订阅状态
    const updatedUser = await updateUserSubscription(
      order.user_uuid,
      subscription_plan,
      "active",
      expires_at,
      order.stripe_session_id || undefined
    );

    console.log("User subscription updated successfully:", {
      user_uuid: order.user_uuid,
      order_no: order.order_no,
      subscription_plan,
      subscription_status: "active",
      subscription_expires_at: expires_at.toISOString(),
      valid_months: order.valid_months,
    });

    return updatedUser;

  } catch (error) {
    console.error("Failed to update user subscription:", {
      order_no: order.order_no,
      user_uuid: order.user_uuid,
      error: error,
    });
    throw error;
  }
}
