import jsPDF from 'jspdf';

export interface CertificateData {
  trackTitle: string;
  trackUuid: string;
  createdAt: string;
  duration: number;
  format: string;
  style?: string;
  mood?: string;
  bpm?: number;
  userEmail?: string;
  userName?: string;
}

export class CertificateGenerator {
  private pdf: jsPDF;
  private pageWidth: number;
  private pageHeight: number;
  // 添加 logo PNG base64 字符串
  private logoBase64 =
    '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';

  constructor() {
    this.pdf = new jsPDF({
      orientation: 'landscape',
      unit: 'mm',
      format: 'a4'
    });
    this.pageWidth = this.pdf.internal.pageSize.getWidth();
    this.pageHeight = this.pdf.internal.pageSize.getHeight();
  }

  generateCertificate(data: CertificateData): Uint8Array {
    this.drawBackground();
    this.drawHeader();
    this.drawTitle();
    this.drawTrackInfo(data);
    this.drawCertificationText(data);
    this.drawFooter(data);
    this.drawBorder();

    return this.pdf.output('arraybuffer') as Uint8Array;
  }

  private drawBackground() {
    // Clean white background
    this.pdf.setFillColor(255, 255, 255);
    this.pdf.rect(0, 0, this.pageWidth, this.pageHeight, 'F');

    // Elegant header background
    this.pdf.setFillColor(248, 250, 252);
    this.pdf.rect(0, 0, this.pageWidth, 60, 'F');

    // Subtle watermark pattern (less dense)
    this.pdf.setFillColor(252, 253, 254);
    for (let i = 40; i < this.pageWidth - 40; i += 30) {
      for (let j = 70; j < this.pageHeight - 50; j += 30) {
        this.pdf.circle(i, j, 0.2, 'F');
      }
    }
  }

  private drawBorder() {
    // Elegant gradient-style border
    this.pdf.setDrawColor(37, 99, 235); // Blue-600
    this.pdf.setLineWidth(3);
    this.pdf.rect(8, 8, this.pageWidth - 16, this.pageHeight - 16);

    // Inner accent border
    this.pdf.setDrawColor(59, 130, 246); // Blue-500
    this.pdf.setLineWidth(1);
    this.pdf.rect(12, 12, this.pageWidth - 24, this.pageHeight - 24);

    // Decorative corner elements
    this.drawCornerDecorations();
  }

  private drawCornerDecorations() {
    const cornerSize = 15;
    const offset = 20;

    this.pdf.setDrawColor(147, 197, 253); // Blue-300
    this.pdf.setLineWidth(2);

    // Top-left corner
    this.pdf.line(offset, offset, offset + cornerSize, offset);
    this.pdf.line(offset, offset, offset, offset + cornerSize);

    // Top-right corner
    this.pdf.line(this.pageWidth - offset, offset, this.pageWidth - offset - cornerSize, offset);
    this.pdf.line(this.pageWidth - offset, offset, this.pageWidth - offset, offset + cornerSize);

    // Bottom-left corner
    this.pdf.line(offset, this.pageHeight - offset, offset + cornerSize, this.pageHeight - offset);
    this.pdf.line(offset, this.pageHeight - offset, offset, this.pageHeight - offset - cornerSize);

    // Bottom-right corner
    this.pdf.line(this.pageWidth - offset, this.pageHeight - offset, this.pageWidth - offset - cornerSize, this.pageHeight - offset);
    this.pdf.line(this.pageWidth - offset, this.pageHeight - offset, this.pageWidth - offset, this.pageHeight - offset - cornerSize);
  }

  private drawHeader() {
    // 插入 PNG base64 logo
    this.pdf.addImage(
      this.logoBase64,
      'PNG',
      30, 25, // x, y
      20, 20 // width, height
    );

    // Company name
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setFontSize(28);
    this.pdf.setTextColor(15, 23, 42); // Slate-900
    this.pdf.text('LoopCraft', 55, 38);

    // Tagline
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(14);
    this.pdf.setTextColor(71, 85, 105); // Slate-600
    this.pdf.text('AI Music Generation Platform', 55, 45);
  }

  private drawTitle() {
    // Main title with elegant styling
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setFontSize(38);
    this.pdf.setTextColor(15, 23, 42); // Slate-900

    const title = 'CERTIFICATE OF AUTHENTICITY';
    const titleWidth = this.pdf.getTextWidth(title);
    const titleX = (this.pageWidth - titleWidth) / 2;

    this.pdf.text(title, titleX, 80);

    // Subtitle with improved styling
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(18);
    this.pdf.setTextColor(71, 85, 105); // Slate-600

    const subtitle = 'AI-Generated Music Track';
    const subtitleWidth = this.pdf.getTextWidth(subtitle);
    const subtitleX = (this.pageWidth - subtitleWidth) / 2;

    this.pdf.text(subtitle, subtitleX, 92);

    // Decorative elements
    this.pdf.setDrawColor(37, 99, 235); // Blue-600
    this.pdf.setLineWidth(1.5);
    this.pdf.line(this.pageWidth / 2 - 50, 98, this.pageWidth / 2 + 50, 98);

    // Small decorative dots
    this.pdf.setFillColor(59, 130, 246); // Blue-500
    this.pdf.circle(this.pageWidth / 2 - 60, 98, 1.5, 'F');
    this.pdf.circle(this.pageWidth / 2 + 60, 98, 1.5, 'F');
  }

  private drawTrackInfo(data: CertificateData) {
    const startY = 115;
    const leftCol = 60;
    const rightCol = 190;

    // Track title with elegant styling
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setFontSize(22);
    this.pdf.setTextColor(15, 23, 42); // Slate-900

    const trackTitle = `"${data.trackTitle}"`;
    const titleWidth = this.pdf.getTextWidth(trackTitle);
    const titleX = (this.pageWidth - titleWidth) / 2;

    this.pdf.text(trackTitle, titleX, startY);

    // Background for track details
    this.pdf.setFillColor(248, 250, 252); // Very light blue-gray
    this.pdf.roundedRect(40, startY + 10, this.pageWidth - 80, 40, 3, 3, 'F');

    // Track details in two columns with improved styling
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(12);
    this.pdf.setTextColor(51, 65, 85); // Slate-700

    let currentY = startY + 22;

    // Left column
    this.drawInfoRow('Track ID:', data.trackUuid.substring(0, 8).toUpperCase(), leftCol, currentY);
    currentY += 10;
    this.drawInfoRow('Duration:', `${data.duration} seconds`, leftCol, currentY);
    currentY += 10;
    this.drawInfoRow('Format:', data.format.toUpperCase(), leftCol, currentY);

    // Right column
    currentY = startY + 22;
    if (data.style) {
      this.drawInfoRow('Style:', data.style, rightCol, currentY);
      currentY += 10;
    }
    if (data.mood) {
      this.drawInfoRow('Mood:', data.mood, rightCol, currentY);
      currentY += 10;
    }
    if (data.bpm) {
      this.drawInfoRow('BPM:', data.bpm.toString(), rightCol, currentY);
    }
  }

  private drawInfoRow(label: string, value: string, x: number, y: number) {
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(71, 85, 105); // Slate-600
    this.pdf.text(label, x, y);

    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setTextColor(15, 23, 42); // Slate-900
    this.pdf.text(value, x + 30, y);
  }

  private drawCertificationText(_data: CertificateData) {
    const startY = 175;
    const margin = 45;
    const textWidth = this.pageWidth - (margin * 2);

    // Background for certification text
    this.pdf.setFillColor(248, 250, 252); // Very light blue-gray
    this.pdf.roundedRect(margin - 10, startY - 10, textWidth + 20, 60, 3, 3, 'F');

    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(13);
    this.pdf.setTextColor(51, 65, 85); // Slate-700

    const certificationText = [
      'This certificate confirms that the above music track was generated using LoopCraft\'s',
      'AI music generation service. The track is an original creation produced by artificial',
      'intelligence algorithms and is owned by the authenticated user.',
      '',
      'The holder of this certificate has the right to use this music for commercial purposes',
      'in accordance with LoopCraft\'s terms of service and applicable copyright laws.'
    ];

    let currentY = startY;
    certificationText.forEach(line => {
      if (line === '') {
        currentY += 8;
      } else {
        const lineWidth = this.pdf.getTextWidth(line);
        const lineX = (this.pageWidth - lineWidth) / 2;
        this.pdf.text(line, lineX, currentY);
        currentY += 8;
      }
    });
  }

  private drawFooter(data: CertificateData) {
    const footerY = this.pageHeight - 35;

    // Footer background
    this.pdf.setFillColor(248, 250, 252);
    this.pdf.rect(0, this.pageHeight - 50, this.pageWidth, 50, 'F');

    // Generation info with improved styling
    this.pdf.setFont('helvetica', 'normal');
    this.pdf.setFontSize(10);
    this.pdf.setTextColor(71, 85, 105); // Slate-600

    const generatedDate = new Date().toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const certificateId = `CERT-${Date.now().toString(36).toUpperCase()}`;

    this.pdf.text(`Generated on: ${generatedDate}`, 30, footerY);
    this.pdf.text(`Certificate ID: ${certificateId}`, 30, footerY + 7);

    // Website with improved styling
    this.pdf.setFont('helvetica', 'bold');
    this.pdf.setTextColor(37, 99, 235); // Blue-600
    const website = 'www.loopcraft.ai';
    const websiteWidth = this.pdf.getTextWidth(website);
    this.pdf.text(website, this.pageWidth - 30 - websiteWidth, footerY + 3);

    // Verification note with better positioning
    this.pdf.setFont('helvetica', 'italic');
    this.pdf.setFontSize(9);
    this.pdf.setTextColor(100, 116, 139); // Slate-500
    const verificationText = `Verify at: https://loopcraft.ai/verify-certificate?id=${certificateId}&track=${data.trackUuid}`;
    const verificationWidth = this.pdf.getTextWidth(verificationText);
    const verificationX = (this.pageWidth - verificationWidth) / 2;
    this.pdf.text(verificationText, verificationX, this.pageHeight - 15);
  }
}
