"use client";

import * as React from "react";
import { cn } from "@/lib/utils";

export interface TechCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: "default" | "glass" | "neon" | "gradient" | "floating";
  glow?: boolean;
  animated?: boolean;
}

const TechCard = React.forwardRef<HTMLDivElement, TechCardProps>(
  ({ className, variant = "default", glow = false, animated = false, children, ...props }, ref) => {
    const getVariantClasses = () => {
      switch (variant) {
        case "glass":
          return "glass-card";
        case "neon":
          return "bg-card border-2 border-primary/30 shadow-lg shadow-primary/10";
        case "gradient":
          return "tech-gradient text-white border-0";
        case "floating":
          return "bg-card border border-border shadow-lg floating";
        default:
          return "bg-card text-card-foreground border border-border";
      }
    };

    return (
      <div
        ref={ref}
        className={cn(
          "rounded-lg shadow-sm transition-all duration-300",
          getVariantClasses(),
          glow && "neon-glow",
          animated && "hover:scale-[1.02] hover:shadow-xl",
          "relative overflow-hidden group",
          className
        )}
        {...props}
      >
        {/* Animated border effect */}
        {variant === "neon" && (
          <div className="absolute inset-0 rounded-lg bg-gradient-to-r from-primary via-neon-purple to-neon-cyan opacity-0 group-hover:opacity-20 transition-opacity duration-300 -z-10" />
        )}
        
        {/* Content */}
        <div className="relative z-10">
          {children}
        </div>
      </div>
    );
  }
);
TechCard.displayName = "TechCard";

const TechCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex flex-col space-y-1.5 p-6", className)}
    {...props}
  />
));
TechCardHeader.displayName = "TechCardHeader";

const TechCardTitle = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("font-semibold leading-none tracking-tight", className)}
    {...props}
  />
));
TechCardTitle.displayName = "TechCardTitle";

const TechCardDescription = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("text-sm text-muted-foreground", className)}
    {...props}
  />
));
TechCardDescription.displayName = "TechCardDescription";

const TechCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn("p-6 pt-0", className)} {...props} />
));
TechCardContent.displayName = "TechCardContent";

const TechCardFooter = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className={cn("flex items-center p-6 pt-0", className)}
    {...props}
  />
));
TechCardFooter.displayName = "TechCardFooter";

export {
  TechCard,
  TechCardHeader,
  TechCardFooter,
  TechCardTitle,
  TechCardDescription,
  TechCardContent,
};
