import React from "react";

export default function Logo() {
  return (
    <div className="relative">
      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M9 18V5l12-2v13" stroke="rgb(139, 92, 246)" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
        <circle cx="6" cy="18" r="3" stroke="rgb(139, 92, 246)" strokeWidth="2.5" fill="none"/>
        <circle cx="18" cy="16" r="3" stroke="rgb(139, 92, 246)" strokeWidth="2.5" fill="none"/>
      </svg>
      <div className="absolute -top-1 -right-1 w-3 h-3 rounded-full animate-pulse"
        style={{
          backgroundColor: "rgb(249 115 22)"
        } as React.CSSProperties}></div>
    </div>
  );
}
