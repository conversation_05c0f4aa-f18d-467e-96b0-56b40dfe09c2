"use client";

import { useEffect, useRef, useState } from "react";
import { cn } from "@/lib/utils";

interface WaveformDisplayProps {
  peaks: number[];
  duration: number;
  currentTime?: number;
  height?: number;
  className?: string;
  interactive?: boolean;
  onSeek?: (time: number) => void;
  color?: string;
  progressColor?: string;
  backgroundColor?: string;
}

export default function WaveformDisplay({
  peaks,
  duration,
  currentTime = 0,
  height = 64,
  className,
  interactive = false,
  onSeek,
  color = "hsl(var(--primary))",
  progressColor = "hsl(var(--primary))",
  backgroundColor = "hsl(var(--muted))",
}: WaveformDisplayProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [canvasSize, setCanvasSize] = useState({ width: 0, height });

  // Update canvas size on container resize
  useEffect(() => {
    const updateSize = () => {
      if (containerRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        setCanvasSize({ width: rect.width, height });
      }
    };

    updateSize();
    window.addEventListener("resize", updateSize);
    return () => window.removeEventListener("resize", updateSize);
  }, [height]);

  // Draw waveform
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || peaks.length === 0) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const { width, height } = canvasSize;
    const dpr = window.devicePixelRatio || 1;

    // Set canvas size for high DPI displays
    canvas.width = width * dpr;
    canvas.height = height * dpr;
    canvas.style.width = `${width}px`;
    canvas.style.height = `${height}px`;
    ctx.scale(dpr, dpr);

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Fill background
    ctx.fillStyle = backgroundColor;
    ctx.fillRect(0, 0, width, height);

    if (peaks.length === 0) return;

    const barWidth = width / peaks.length;
    const progressPosition = (currentTime / duration) * width;

    // Draw waveform bars
    peaks.forEach((peak, index) => {
      const barHeight = Math.round(Math.max(2, peak * height * 0.8)); // Minimum 2px height
      const x = index * barWidth;
      const y = Math.round((height - barHeight) / 2);

      // Determine color based on progress
      const isPlayed = x < progressPosition;
      ctx.fillStyle = isPlayed ? progressColor : color;
      ctx.globalAlpha = isPlayed ? 1 : 0.4;

      // Draw bar with rounded corners
      const radius = Math.min(barWidth / 4, 2);
      ctx.beginPath();
      ctx.roundRect(x + barWidth * 0.1, y, barWidth * 0.8, barHeight, radius);
      ctx.fill();
    });

    // Draw progress line
    if (currentTime > 0 && duration > 0) {
      ctx.globalAlpha = 1;
      ctx.strokeStyle = progressColor;
      ctx.lineWidth = 2;
      ctx.beginPath();
      ctx.moveTo(progressPosition, 0);
      ctx.lineTo(progressPosition, height);
      ctx.stroke();
    }
  }, [peaks, canvasSize, currentTime, duration, color, progressColor, backgroundColor]);

  const handleClick = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!interactive || !onSeek || !containerRef.current) return;

    const rect = containerRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const progress = x / rect.width;
    const seekTime = progress * duration;

    onSeek(Math.max(0, Math.min(duration, seekTime)));
  };

  const handleMouseMove = (event: React.MouseEvent<HTMLCanvasElement>) => {
    if (!interactive) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    canvas.style.cursor = "pointer";
  };

  return (
    <div
      ref={containerRef}
      className={cn(
        "relative w-full rounded-md overflow-hidden",
        interactive && "cursor-pointer",
        className
      )}
      style={{ height: `${height}px` }}
    >
      <canvas
        ref={canvasRef}
        onClick={handleClick}
        onMouseMove={handleMouseMove}
        className="w-full h-full"
      />
      
      {/* Loading state */}
      {peaks.length === 0 && (
        <div className="absolute inset-0 flex items-center justify-center bg-muted">
          <div className="flex space-x-1">
            {Array.from({ length: 20 }).map((_, i) => {
              // Use deterministic height based on index to avoid hydration mismatch
              const height = ((i * 7) % 60) + 20; // Deterministic pseudo-random height
              return (
                <div
                  key={i}
                  className="w-1 bg-muted-foreground/30 rounded-full animate-pulse"
                  style={{
                    height: `${height}%`,
                    animationDelay: `${i * 0.1}s`,
                  }}
                />
              );
            })}
          </div>
        </div>
      )}

      {/* Time indicators */}
      {interactive && duration > 0 && (
        <div className="absolute bottom-0 left-0 right-0 flex justify-between text-xs text-muted-foreground px-1">
          <span>0:00</span>
          <span>
            {Math.floor(duration / 60)}:{(Math.floor(duration % 60)).toString().padStart(2, "0")}
          </span>
        </div>
      )}
    </div>
  );
}

// Utility component for simple waveform bars without canvas
export function SimpleWaveform({
  peaks,
  currentTime = 0,
  duration,
  height = 40,
  className,
  interactive = false,
  onSeek,
}: Omit<WaveformDisplayProps, "color" | "progressColor" | "backgroundColor">) {
  const handleBarClick = (index: number) => {
    if (!interactive || !onSeek) return;
    
    const progress = index / peaks.length;
    const seekTime = progress * duration;
    onSeek(seekTime);
  };

  return (
    <div 
      className={cn("flex items-end gap-px", className)}
      style={{ height: `${height}px` }}
    >
      {peaks.map((peak, index) => {
        const barHeight = Math.round(Math.max(2, peak * height));
        const progress = currentTime / duration;
        const isPlayed = index / peaks.length < progress;
        
        return (
          <div
            key={index}
            className={cn(
              "flex-1 rounded-sm transition-colors",
              isPlayed 
                ? "bg-primary" 
                : "bg-primary/30",
              interactive && "cursor-pointer hover:bg-primary/60"
            )}
            style={{ height: `${barHeight}px` }}
            onClick={() => handleBarClick(index)}
          />
        );
      })}
    </div>
  );
}
