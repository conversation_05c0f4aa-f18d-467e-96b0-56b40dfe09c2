"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  Filter, 
  Music, 
  Play, 
  Pause,
  Download,
  Heart,
  Share2,
  Clock,
  Zap,
  Sparkles
} from "lucide-react";
import { cn } from "@/lib/utils";
import TrackCard from "@/components/music/track/track-card";
import { Track } from "@/types/music";

// Mock data for public music tracks
const FEATURED_TRACKS: Track[] = [
  {
    uuid: "explore-1",
    generation_uuid: "gen-1",
    user_uuid: "user-1",
    title: "Upbeat Electronic Loop",
    prompt: "Energetic electronic music with driving beats",
    style: "electronic",
    mood: "upbeat",
    bpm: 128,
    duration: 30,
    file_url: "/demo/track1.mp3",
    file_format: "mp3" as const,
    download_count: 1250,
    is_public: true,
    created_at: "2024-01-15T10:00:00Z",
    waveform_data: {
      peaks: Array.from({ length: 100 }, (_, i) => Math.sin(i * 0.15) * 0.4 + 0.5),
      duration: 30,
      sample_rate: 44100,
    },
    loop_verification: {
      is_seamless: true,
      verification_score: "0.95",
    },
  },
  {
    uuid: "explore-2",
    generation_uuid: "gen-2", 
    user_uuid: "user-2",
    title: "Ambient Chill Vibes",
    prompt: "Peaceful ambient soundscape for relaxation",
    style: "ambient",
    mood: "calm",
    bpm: 85,
    duration: 60,
    file_url: "/demo/track2.mp3",
    file_format: "mp3" as const,
    download_count: 890,
    is_public: true,
    created_at: "2024-01-14T15:30:00Z",
    waveform_data: {
      peaks: Array.from({ length: 100 }, (_, i) => Math.sin(i * 0.1) * 0.3 + 0.4),
      duration: 60,
      sample_rate: 44100,
    },
    loop_verification: {
      is_seamless: true,
      verification_score: "0.88",
    },
  },
  {
    uuid: "explore-3",
    generation_uuid: "gen-3",
    user_uuid: "user-3", 
    title: "Corporate Success",
    prompt: "Professional corporate background music",
    style: "corporate",
    mood: "uplifting",
    bpm: 110,
    duration: 45,
    file_url: "/demo/track3.mp3",
    file_format: "mp3" as const,
    download_count: 2100,
    is_public: true,
    created_at: "2024-01-13T09:15:00Z",
    waveform_data: {
      peaks: Array.from({ length: 100 }, (_, i) => Math.sin(i * 0.12) * 0.35 + 0.45),
      duration: 45,
      sample_rate: 44100,
    },
    loop_verification: {
      is_seamless: true,
      verification_score: "0.92",
    },
  },
  {
    uuid: "explore-4",
    generation_uuid: "gen-4",
    user_uuid: "user-4",
    title: "Cinematic Drama",
    prompt: "Epic cinematic music with orchestral elements",
    style: "cinematic",
    mood: "dramatic",
    bpm: 95,
    duration: 60,
    file_url: "/demo/track4.mp3",
    file_format: "mp3" as const,
    download_count: 1680,
    is_public: true,
    created_at: "2024-01-12T14:20:00Z",
    waveform_data: {
      peaks: Array.from({ length: 100 }, (_, i) => Math.sin(i * 0.08) * 0.5 + 0.5),
      duration: 60,
      sample_rate: 44100,
    },
    loop_verification: {
      is_seamless: true,
      verification_score: "0.90",
    },
  },
];

const STYLES = ["all", "electronic", "ambient", "corporate", "cinematic", "acoustic", "jazz", "rock"];
const MOODS = ["all", "upbeat", "calm", "dramatic", "uplifting", "energetic", "peaceful", "mysterious"];
const DURATIONS = ["all", "15", "30", "60"];

export default function ExploreMusic() {
  const [tracks, setTracks] = useState<Track[]>(FEATURED_TRACKS);
  const [filteredTracks, setFilteredTracks] = useState<Track[]>(FEATURED_TRACKS);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedStyle, setSelectedStyle] = useState("all");
  const [selectedMood, setSelectedMood] = useState("all");
  const [selectedDuration, setSelectedDuration] = useState("all");
  const [sortBy, setSortBy] = useState("popular"); // popular, recent, title
  const [isLoading, setIsLoading] = useState(false);

  // Filter and search logic
  useEffect(() => {
    let filtered = [...tracks];

    // Search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(track => 
        track.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.prompt?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.style?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        track.mood?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Style filter
    if (selectedStyle !== "all") {
      filtered = filtered.filter(track => track.style === selectedStyle);
    }

    // Mood filter
    if (selectedMood !== "all") {
      filtered = filtered.filter(track => track.mood === selectedMood);
    }

    // Duration filter
    if (selectedDuration !== "all") {
      filtered = filtered.filter(track => track.duration === parseInt(selectedDuration));
    }

    // Sort
    switch (sortBy) {
      case "popular":
        filtered.sort((a, b) => (b.download_count || 0) - (a.download_count || 0));
        break;
      case "recent":
        filtered.sort((a, b) => new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime());
        break;
      case "title":
        filtered.sort((a, b) => (a.title || "").localeCompare(b.title || ""));
        break;
    }

    setFilteredTracks(filtered);
  }, [tracks, searchQuery, selectedStyle, selectedMood, selectedDuration, sortBy]);

  const handleTrackPlay = (track: Track) => {
    console.log("Playing track:", track.title);
    // TODO: Implement global audio player
  };

  const handleTrackDownload = (track: Track) => {
    console.log("Downloading track:", track.title);
    // TODO: Implement download functionality
  };

  const handleTrackLike = (track: Track) => {
    console.log("Liking track:", track.title);
    // TODO: Implement like functionality
  };

  const handleTrackShare = (track: Track) => {
    console.log("Sharing track:", track.title);
    // TODO: Implement share functionality
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedStyle("all");
    setSelectedMood("all");
    setSelectedDuration("all");
    setSortBy("popular");
  };

  return (
    <div className="container py-8">
      {/* Header */}
      <div className="text-center mb-12">
        <div className="flex items-center justify-center mb-4">
          <Badge variant="secondary" className="px-4 py-2 text-sm">
            <Sparkles className="mr-2 h-4 w-4" />
            Community Showcase
          </Badge>
        </div>
        
        <h1 className="text-4xl font-bold mb-4">
          Explore AI Music{" "}
          <span className="bg-gradient-to-r from-primary via-primary to-secondary bg-clip-text text-transparent">
            Loops
          </span>
        </h1>
        
        <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
          Discover amazing AI-generated music loops created by our community. 
          Find inspiration and explore different styles, moods, and genres.
        </p>
      </div>

      {/* Filters and Search */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter & Search
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search tracks..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Style Filter */}
            <Select value={selectedStyle} onValueChange={setSelectedStyle}>
              <SelectTrigger>
                <SelectValue placeholder="Style" />
              </SelectTrigger>
              <SelectContent>
                {STYLES.map(style => (
                  <SelectItem key={style} value={style}>
                    {style === "all" ? "All Styles" : style.charAt(0).toUpperCase() + style.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Mood Filter */}
            <Select value={selectedMood} onValueChange={setSelectedMood}>
              <SelectTrigger>
                <SelectValue placeholder="Mood" />
              </SelectTrigger>
              <SelectContent>
                {MOODS.map(mood => (
                  <SelectItem key={mood} value={mood}>
                    {mood === "all" ? "All Moods" : mood.charAt(0).toUpperCase() + mood.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Duration Filter */}
            <Select value={selectedDuration} onValueChange={setSelectedDuration}>
              <SelectTrigger>
                <SelectValue placeholder="Duration" />
              </SelectTrigger>
              <SelectContent>
                {DURATIONS.map(duration => (
                  <SelectItem key={duration} value={duration}>
                    {duration === "all" ? "All Durations" : `${duration}s`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="popular">Most Popular</SelectItem>
                <SelectItem value="recent">Most Recent</SelectItem>
                <SelectItem value="title">Title A-Z</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              Showing {filteredTracks.length} of {tracks.length} tracks
            </div>
            <Button variant="outline" size="sm" onClick={clearFilters}>
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {isLoading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading tracks...</p>
        </div>
      ) : filteredTracks.length === 0 ? (
        <div className="text-center py-12">
          <Music className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">No tracks found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your filters or search terms
          </p>
          <Button variant="outline" onClick={clearFilters}>
            Clear Filters
          </Button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredTracks.map((track) => (
            <TrackCard
              key={track.uuid}
              track={track}
              showUser={false}
              onPlay={handleTrackPlay}
              onDownload={handleTrackDownload}
              onLike={handleTrackLike}
              onShare={handleTrackShare}
            />
          ))}
        </div>
      )}

      {/* Load More */}
      {filteredTracks.length > 0 && (
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Load More Tracks
          </Button>
        </div>
      )}
    </div>
  );
}
