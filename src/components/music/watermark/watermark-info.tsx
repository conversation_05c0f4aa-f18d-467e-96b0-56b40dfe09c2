"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Shield, 
  ShieldCheck, 
  ShieldX, 
  Crown, 
  Zap, 
  Info,
  ExternalLink,
  Volume2,
  VolumeX
} from "lucide-react";
import { cn } from "@/lib/utils";

interface WatermarkInfoProps {
  track?: {
    uuid: string;
    title?: string;
    has_watermark?: boolean;
    is_premium?: boolean;
    original_file_url?: string;
  };
  userPlan?: "free" | "professional" | "commercial";
  className?: string;
}

interface WatermarkStatus {
  has_watermark: boolean;
  watermark_type: string;
  removal_available: boolean;
  upgrade_required: boolean;
  upgrade_message: string;
}

export default function WatermarkInfo({ track, userPlan = "free", className }: WatermarkInfoProps) {
  const [watermarkStatus, setWatermarkStatus] = useState<WatermarkStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    loadWatermarkStatus();
  }, [track?.uuid]);

  const loadWatermarkStatus = async () => {
    if (!track?.uuid) return;

    setIsLoading(true);
    try {
      // In a real implementation, this would call the API
      // For now, we'll simulate the response
      const mockStatus: WatermarkStatus = {
        has_watermark: track.has_watermark ?? (userPlan === "free"),
        watermark_type: "audio",
        removal_available: userPlan === "free",
        upgrade_required: userPlan === "free",
        upgrade_message: userPlan === "free" 
          ? "Upgrade to Professional plan to remove watermarks and unlock high-quality downloads."
          : "",
      };

      setWatermarkStatus(mockStatus);
    } catch (error) {
      console.error("Failed to load watermark status:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleUpgrade = () => {
    // Redirect to pricing page or open upgrade modal
    window.open("/pricing", "_blank");
  };

  const downloadOriginal = async () => {
    if (!track?.original_file_url) return;

    try {
      const link = document.createElement("a");
      link.href = track.original_file_url;
      link.download = `${track.title || "track"}-original.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error("Download failed:", error);
    }
  };

  if (!watermarkStatus) {
    return null;
  }

  const getStatusIcon = () => {
    if (watermarkStatus.has_watermark) {
      return <ShieldX className="h-5 w-5 text-orange-500" />;
    } else {
      return <ShieldCheck className="h-5 w-5 text-green-500" />;
    }
  };

  const getStatusBadge = () => {
    if (watermarkStatus.has_watermark) {
      return (
        <Badge variant="outline" className="border-orange-300 text-orange-700">
          <Volume2 className="h-3 w-3 mr-1" />
          Watermarked
        </Badge>
      );
    } else {
      return (
        <Badge variant="outline" className="border-green-300 text-green-700">
          <VolumeX className="h-3 w-3 mr-1" />
          Clean Audio
        </Badge>
      );
    }
  };

  const getPlanBadge = () => {
    const planConfig = {
      free: { label: "Free", color: "bg-gray-500", icon: "🆓" },
      professional: { label: "Professional", color: "bg-purple-500", icon: "💼" },
      commercial: { label: "Commercial", color: "bg-green-500", icon: "🏢" },
    };

    const config = planConfig[userPlan];
    
    return (
      <Badge variant="secondary" className={cn("text-white", config.color)}>
        {config.icon} {config.label}
      </Badge>
    );
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {getStatusIcon()}
          Audio Quality & Licensing
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Status */}
        <div className="flex items-center justify-between">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              {getStatusBadge()}
              {getPlanBadge()}
            </div>
            <p className="text-sm text-muted-foreground">
              {watermarkStatus.has_watermark 
                ? "This track includes audio watermarks" 
                : "This track is watermark-free"
              }
            </p>
          </div>
        </div>

        {/* Watermark Details */}
        {watermarkStatus.has_watermark && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-medium">About Watermarks:</p>
                <ul className="text-sm space-y-1 ml-4">
                  <li>• Subtle "LoopCraft" audio markers are added to free downloads</li>
                  <li>• Watermarks help protect our content and support the platform</li>
                  <li>• They don't significantly impact the listening experience</li>
                  <li>• Professional users get completely clean audio files</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Upgrade Prompt */}
        {watermarkStatus.upgrade_required && watermarkStatus.upgrade_message && (
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <Crown className="h-5 w-5 text-purple-500 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-medium text-purple-900 mb-2">Upgrade for Premium Quality</h4>
                <p className="text-sm text-purple-700 mb-3">
                  {watermarkStatus.upgrade_message}
                </p>
                <div className="flex gap-2">
                  <Button
                    onClick={handleUpgrade}
                    size="sm"
                    className="bg-purple-600 hover:bg-purple-700"
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Upgrade Now
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.open("/pricing", "_blank")}
                  >
                    <ExternalLink className="h-4 w-4 mr-2" />
                    View Plans
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Premium Features */}
        {!watermarkStatus.has_watermark && (
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-start gap-3">
              <ShieldCheck className="h-5 w-5 text-green-500 mt-0.5" />
              <div className="flex-1">
                <h4 className="font-medium text-green-900 mb-2">Premium Quality Unlocked</h4>
                <div className="text-sm text-green-700 space-y-1">
                  <p>✓ Watermark-free audio</p>
                  <p>✓ High-quality downloads</p>
                  <p>✓ Commercial usage rights</p>
                  <p>✓ Unlimited downloads</p>
                </div>
                {track?.original_file_url && (
                  <Button
                    onClick={downloadOriginal}
                    variant="outline"
                    size="sm"
                    className="mt-3 border-green-300 text-green-700 hover:bg-green-50"
                  >
                    Download Original Quality
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Usage Rights */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Usage Rights</h4>
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                userPlan !== "free" ? "bg-green-500" : "bg-red-500"
              )} />
              <span>Commercial Use</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-500" />
              <span>Modification</span>
            </div>
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                userPlan !== "free" ? "bg-green-500" : "bg-red-500"
              )} />
              <span>Distribution</span>
            </div>
            <div className="flex items-center gap-2">
              <div className={cn(
                "w-3 h-3 rounded-full",
                userPlan === "free" ? "bg-red-500" : "bg-green-500"
              )} />
              <span>No Attribution Required</span>
            </div>
          </div>
        </div>

        {/* Legal Notice */}
        <div className="text-xs text-muted-foreground border-t pt-3">
          <p>
            By downloading this track, you agree to our{" "}
            <a href="/terms" className="underline hover:text-primary">Terms of Service</a>{" "}
            and{" "}
            <a href="/license" className="underline hover:text-primary">License Agreement</a>.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
