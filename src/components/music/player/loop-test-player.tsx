"use client";

import { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  RotateCcw,
  CheckCircle,
  AlertCircle,
  Repeat
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Track } from "@/types/music";

interface LoopTestPlayerProps {
  track: Track;
  autoStart?: boolean;
  maxLoops?: number;
  className?: string;
  onTestComplete?: (loopCount: number) => void;
  onLoopComplete?: (currentLoop: number) => void;
}

export default function LoopTestPlayer({
  track,
  autoStart = false,
  maxLoops = 3,
  className,
  onTestComplete,
  onLoopComplete,
}: LoopTestPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(track.duration || 0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  // Loop test specific states
  const [currentLoop, setCurrentLoop] = useState(0);
  const [isTestActive, setIsTestActive] = useState(false);
  const [testCompleted, setTestCompleted] = useState(false);
  const [loopHistory, setLoopHistory] = useState<number[]>([]);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handleEnded = () => {
      if (isTestActive && currentLoop < maxLoops) {
        // Continue loop test
        const nextLoop = currentLoop + 1;
        setCurrentLoop(nextLoop);
        setLoopHistory(prev => [...prev, Date.now()]);
        onLoopComplete?.(nextLoop);
        
        if (nextLoop >= maxLoops) {
          // Test completed
          setIsTestActive(false);
          setTestCompleted(true);
          setIsPlaying(false);
          onTestComplete?.(nextLoop);
        } else {
          // Restart for next loop
          audio.currentTime = 0;
          audio.play();
        }
      } else {
        // Normal playback ended
        setIsPlaying(false);
        setCurrentTime(0);
      }
    };

    audio.addEventListener("timeupdate", updateTime);
    audio.addEventListener("loadedmetadata", updateDuration);
    audio.addEventListener("loadstart", handleLoadStart);
    audio.addEventListener("canplay", handleCanPlay);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("timeupdate", updateTime);
      audio.removeEventListener("loadedmetadata", updateDuration);
      audio.removeEventListener("loadstart", handleLoadStart);
      audio.removeEventListener("canplay", handleCanPlay);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [isTestActive, currentLoop, maxLoops, onTestComplete, onLoopComplete]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume;
    }
  }, [volume, isMuted]);

  useEffect(() => {
    if (autoStart && !testCompleted) {
      startLoopTest();
    }
  }, [autoStart, testCompleted]);

  const startLoopTest = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      setIsTestActive(true);
      setCurrentLoop(1);
      setTestCompleted(false);
      setLoopHistory([Date.now()]);
      setCurrentTime(0);
      audio.currentTime = 0;
      await audio.play();
      setIsPlaying(true);
    } catch (error) {
      console.error("Loop test start error:", error);
      setIsTestActive(false);
    }
  };

  const stopLoopTest = () => {
    const audio = audioRef.current;
    if (!audio) return;

    audio.pause();
    setIsPlaying(false);
    setIsTestActive(false);
    setCurrentTime(0);
    audio.currentTime = 0;
  };

  const resetTest = () => {
    stopLoopTest();
    setCurrentLoop(0);
    setTestCompleted(false);
    setLoopHistory([]);
  };

  const togglePlay = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      if (isPlaying) {
        audio.pause();
        setIsPlaying(false);
      } else {
        await audio.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error("Audio playback error:", error);
    }
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0] / 100);
    setIsMuted(false);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const getLoopProgress = () => {
    if (!isTestActive) return 0;
    return ((currentLoop - 1) / maxLoops) * 100 + (currentTime / duration) * (100 / maxLoops);
  };

  return (
    <Card className={cn("p-4 space-y-4", className)}>
      <audio
        ref={audioRef}
        src={track.file_url}
        preload="metadata"
      />

      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Repeat className="h-5 w-5" />
          Loop Test Player
          {track.loop_verification?.is_seamless && (
            <CheckCircle className="h-4 w-4 text-green-500" />
          )}
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Track Info */}
        <div className="space-y-2">
          <h3 className="font-medium text-sm">
            {track.title || "Untitled Track"}
          </h3>
          <div className="flex gap-2 flex-wrap">
            {track.style && (
              <Badge variant="secondary" className="text-xs">
                {track.style}
              </Badge>
            )}
            {track.mood && (
              <Badge variant="outline" className="text-xs">
                {track.mood}
              </Badge>
            )}
            {track.bpm && (
              <Badge variant="outline" className="text-xs">
                {track.bpm} BPM
              </Badge>
            )}
            {track.loop_verification && (
              <Badge 
                variant={track.loop_verification.is_seamless ? "default" : "destructive"} 
                className="text-xs"
              >
                {track.loop_verification.is_seamless ? "Seamless" : "Not Seamless"}
              </Badge>
            )}
          </div>
        </div>

        {/* Loop Test Status */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">
              Loop Test Progress
            </span>
            <span className="text-sm text-muted-foreground">
              {currentLoop}/{maxLoops} loops
            </span>
          </div>
          <Progress value={getLoopProgress()} className="h-2" />
          
          {testCompleted && (
            <div className="flex items-center gap-2 text-sm text-green-600">
              <CheckCircle className="h-4 w-4" />
              Test completed! Played {maxLoops} seamless loops.
            </div>
          )}
        </div>

        {/* Current Track Progress */}
        <div className="space-y-2">
          <Slider
            value={[duration > 0 ? (currentTime / duration) * 100 : 0]}
            max={100}
            step={0.1}
            className="w-full"
            disabled={isTestActive}
          />
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>{formatTime(currentTime)}</span>
            <span>{formatTime(duration)}</span>
          </div>
        </div>

        {/* Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {!isTestActive ? (
              <Button
                variant="default"
                size="sm"
                onClick={startLoopTest}
                disabled={isLoading || testCompleted}
                className="gap-2"
              >
                <Repeat className="h-4 w-4" />
                Start Loop Test
              </Button>
            ) : (
              <Button
                variant="destructive"
                size="sm"
                onClick={stopLoopTest}
                className="gap-2"
              >
                <Pause className="h-4 w-4" />
                Stop Test
              </Button>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={resetTest}
              disabled={isLoading}
              className="gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset
            </Button>

            {!isTestActive && (
              <Button
                variant="ghost"
                size="sm"
                onClick={togglePlay}
                disabled={isLoading}
                className="w-10 h-10 rounded-full"
              >
                {isLoading ? (
                  <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                ) : isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>

          {/* Volume Control */}
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMute}
            >
              {isMuted || volume === 0 ? (
                <VolumeX className="h-4 w-4" />
              ) : (
                <Volume2 className="h-4 w-4" />
              )}
            </Button>
            <Slider
              value={[isMuted ? 0 : volume * 100]}
              onValueChange={handleVolumeChange}
              max={100}
              step={1}
              className="w-20"
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
