"use client";

import { useState, useRef, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Download,
  Repeat,
  SkipBack,
  SkipForward
} from "lucide-react";
import { cn } from "@/lib/utils";

interface AudioPlayerProps {
  track: {
    uuid: string;
    title?: string;
    file_url: string;
    duration: number;
    style?: string;
    mood?: string;
    bpm?: number;
    waveform_data?: {
      peaks: number[];
      duration: number;
      sample_rate: number;
    };
  };
  autoPlay?: boolean;
  showWaveform?: boolean;
  showDownload?: boolean;
  className?: string;
  onPlay?: () => void;
  onPause?: () => void;
  onEnded?: () => void;
}

export default function AudioPlayer({
  track,
  autoPlay = false,
  showWaveform = true,
  showDownload = true,
  className,
  onPlay,
  onPause,
  onEnded,
}: AudioPlayerProps) {
  const audioRef = useRef<HTMLAudioElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(track.duration || 0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [isLooping, setIsLooping] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const updateTime = () => setCurrentTime(audio.currentTime);
    const updateDuration = () => setDuration(audio.duration);
    const handleLoadStart = () => setIsLoading(true);
    const handleCanPlay = () => setIsLoading(false);
    const handleEnded = () => {
      setIsPlaying(false);
      if (!isLooping) {
        setCurrentTime(0);
      }
      onEnded?.();
    };

    audio.addEventListener("timeupdate", updateTime);
    audio.addEventListener("loadedmetadata", updateDuration);
    audio.addEventListener("loadstart", handleLoadStart);
    audio.addEventListener("canplay", handleCanPlay);
    audio.addEventListener("ended", handleEnded);

    return () => {
      audio.removeEventListener("timeupdate", updateTime);
      audio.removeEventListener("loadedmetadata", updateDuration);
      audio.removeEventListener("loadstart", handleLoadStart);
      audio.removeEventListener("canplay", handleCanPlay);
      audio.removeEventListener("ended", handleEnded);
    };
  }, [isLooping, onEnded]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = isMuted ? 0 : volume;
    }
  }, [volume, isMuted]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.loop = isLooping;
    }
  }, [isLooping]);

  const togglePlay = async () => {
    const audio = audioRef.current;
    if (!audio) return;

    try {
      if (isPlaying) {
        audio.pause();
        setIsPlaying(false);
        onPause?.();
      } else {
        await audio.play();
        setIsPlaying(true);
        onPlay?.();
      }
    } catch (error) {
      console.error("Audio playback error:", error);
    }
  };

  const handleSeek = (value: number[]) => {
    const audio = audioRef.current;
    if (!audio) return;

    const newTime = (value[0] / 100) * duration;
    audio.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (value: number[]) => {
    setVolume(value[0] / 100);
    setIsMuted(false);
  };

  const toggleMute = () => {
    setIsMuted(!isMuted);
  };

  const toggleLoop = () => {
    setIsLooping(!isLooping);
  };

  const skipBackward = () => {
    const audio = audioRef.current;
    if (!audio) return;
    audio.currentTime = Math.max(0, audio.currentTime - 10);
  };

  const skipForward = () => {
    const audio = audioRef.current;
    if (!audio) return;
    audio.currentTime = Math.min(duration, audio.currentTime + 10);
  };

  const formatTime = (time: number) => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const downloadTrack = () => {
    if (typeof document === 'undefined') return;

    const link = document.createElement("a");
    link.href = track.file_url;
    link.download = `${track.title || "track"}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Card className={cn("p-4 space-y-4", className)}>
      <audio
        ref={audioRef}
        src={track.file_url}
        preload="metadata"
        autoPlay={autoPlay}
      />

      {/* Track Info */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <h3 className="font-medium text-sm">
            {track.title || "Untitled Track"}
          </h3>
          <div className="flex gap-2">
            {track.style && (
              <Badge variant="secondary" className="text-xs">
                {track.style}
              </Badge>
            )}
            {track.mood && (
              <Badge variant="outline" className="text-xs">
                {track.mood}
              </Badge>
            )}
            {track.bpm && (
              <Badge variant="outline" className="text-xs">
                {track.bpm} BPM
              </Badge>
            )}
          </div>
        </div>
        {showDownload && (
          <Button
            variant="ghost"
            size="sm"
            onClick={downloadTrack}
            className="shrink-0"
          >
            <Download className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Waveform Visualization */}
      {showWaveform && track.waveform_data && (
        <div className="relative h-16 bg-muted rounded-md overflow-hidden">
          <div className="flex items-end h-full px-1">
            {track.waveform_data.peaks.map((peak, index) => (
              <div
                key={index}
                className="flex-1 bg-primary/60 mx-px rounded-sm"
                style={{
                  height: `${Math.max(2, peak * 100)}%`,
                  opacity: index / track.waveform_data!.peaks.length < currentTime / duration ? 1 : 0.3,
                }}
              />
            ))}
          </div>
          {/* Progress indicator */}
          <div
            className="absolute top-0 bottom-0 w-0.5 bg-primary"
            style={{
              left: `${(currentTime / duration) * 100}%`,
            }}
          />
        </div>
      )}

      {/* Progress Bar */}
      <div className="space-y-2">
        <Slider
          value={[duration > 0 ? (currentTime / duration) * 100 : 0]}
          onValueChange={handleSeek}
          max={100}
          step={0.1}
          className="w-full"
        />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>{formatTime(currentTime)}</span>
          <span>{formatTime(duration)}</span>
        </div>
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={skipBackward}
            disabled={isLoading}
          >
            <SkipBack className="h-4 w-4" />
          </Button>
          
          <Button
            variant="default"
            size="sm"
            onClick={togglePlay}
            disabled={isLoading}
            className="w-10 h-10 rounded-full"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={skipForward}
            disabled={isLoading}
          >
            <SkipForward className="h-4 w-4" />
          </Button>

          <Button
            variant="ghost"
            size="sm"
            onClick={toggleLoop}
            className={cn(isLooping && "text-primary")}
          >
            <Repeat className="h-4 w-4" />
          </Button>
        </div>

        {/* Volume Control */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleMute}
          >
            {isMuted || volume === 0 ? (
              <VolumeX className="h-4 w-4" />
            ) : (
              <Volume2 className="h-4 w-4" />
            )}
          </Button>
          <Slider
            value={[isMuted ? 0 : volume * 100]}
            onValueChange={handleVolumeChange}
            max={100}
            step={1}
            className="w-20"
          />
        </div>
      </div>
    </Card>
  );
}
