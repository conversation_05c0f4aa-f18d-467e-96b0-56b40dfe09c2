import { Metadata } from "next";
import { notFound } from "next/navigation";
import { findTrackBySlug, findTrackByUuid } from "@/models/track";
import { db } from "@/db";
import { tracks } from "@/db/schema";
import { like } from "drizzle-orm";
import { findLoopVerificationByTrackUuid } from "@/models/loop-verification";
import TrackDetailPage from "@/components/music/track/track-detail-page";

interface PageProps {
  params: Promise<{
    locale: string;
    slug: string;
  }>;
}

// Helper function to extract UUID from slug
function extractUuidFromSlug(slug: string): string | null {
  // Slug format: {prompt-keywords}-{bpm}bpm-{uuid}
  const parts = slug.split('-');
  const lastPart = parts[parts.length - 1];

  // Check if last part looks like a UUID (basic validation)
  // Support both full UUID (32+ chars) and short UUID (8 chars)
  if (lastPart && (lastPart.length >= 32 || lastPart.length === 8)) {
    return lastPart;
  }

  return null;
}

// Helper function to find track by short UUID
async function findTrackByShortUuid(shortUuid: string) {
  const [track] = await db()
    .select()
    .from(tracks)
    .where(like(tracks.uuid, `${shortUuid}%`))
    .limit(1);

  return track;
}

// Generate metadata for SEO
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale, slug } = await params;
  
  try {
    // Try to find track by slug first, then by UUID
    let track = await findTrackBySlug(slug);
    
    if (!track) {
      const uuid = extractUuidFromSlug(slug);
      if (uuid) {
        // Try full UUID first, then short UUID
        track = await findTrackByUuid(uuid);
        if (!track && uuid.length === 8) {
          track = await findTrackByShortUuid(uuid);
        }
      }
    }

    if (!track) {
      return {
        title: "Track Not Found",
        description: "The requested music loop could not be found.",
      };
    }

    // Get loop verification data
    const verification = await findLoopVerificationByTrackUuid(track.uuid);

    const title = `${track.title || "AI Music Loop"} - ${track.bpm ? track.bpm + " BPM" : ""} ${track.style || ""} Loop`;
    const description = `${track.prompt || "AI-generated seamless music loop"} - ${track.duration}s ${track.style || "music"} loop at ${track.bpm || "variable"} BPM. ${verification?.is_seamless ? "Verified seamless loop" : "Music loop"} for content creators.`;

    const canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}loops/${slug}`;

    return {
      title,
      description,
      keywords: [
        "AI music loop",
        "seamless loop",
        "background music",
        track.style,
        track.mood,
        `${track.bpm} BPM`,
        "content creation",
        "royalty free music",
      ].filter(Boolean).join(", "),
      openGraph: {
        title,
        description,
        type: "music.song",
        url: canonicalUrl,
        siteName: "LoopCraft",
        locale: locale,
        images: [
          {
            url: `${process.env.NEXT_PUBLIC_WEB_URL}/api/og/track?uuid=${track.uuid}`,
            width: 1200,
            height: 630,
            alt: title,
          },
        ],
      },
      twitter: {
        card: "summary_large_image",
        title,
        description,
        images: [`${process.env.NEXT_PUBLIC_WEB_URL}/api/og/track?uuid=${track.uuid}`],
      },
      alternates: {
        canonical: canonicalUrl,
      },
      other: {
        // Schema.org structured data for music
        "music:duration": track.duration.toString(),
        "music:musician": "LoopCraft AI",
        "music:album": track.style || "AI Generated Loops",
      },
    };
  } catch (error) {
    console.error("Error generating metadata:", error);
    return {
      title: "Music Loop",
      description: "AI-generated seamless music loop for content creators.",
    };
  }
}

export default async function LoopDetailPage({ params }: PageProps) {
  const { locale, slug } = await params;

  try {
    // Try to find track by slug first, then by UUID
    let track = await findTrackBySlug(slug);
    
    if (!track) {
      const uuid = extractUuidFromSlug(slug);
      if (uuid) {
        // Try full UUID first, then short UUID
        track = await findTrackByUuid(uuid);
        if (!track && uuid.length === 8) {
          track = await findTrackByShortUuid(uuid);
        }
      }
    }

    if (!track) {
      notFound();
    }

    // Get additional track data
    const verification = await findLoopVerificationByTrackUuid(track.uuid);

    // Prepare track data with verification
    const trackWithVerification = {
      ...track,
      title: track.title || undefined,
      slug: track.slug || undefined,
      style: track.style || undefined,
      mood: track.mood || undefined,
      bpm: track.bpm || undefined,
      key_signature: track.key_signature || undefined,
      file_size: track.file_size || undefined,
      file_format: (track.file_format as "mp3" | "wav") || "mp3",
      waveform_data: track.waveform_data as { peaks: number[]; duration: number; sample_rate: number; } | undefined,
      metadata: track.metadata || undefined,
      created_at: track.created_at?.toISOString() || undefined,
      updated_at: track.updated_at?.toISOString() || undefined,
      original_file_url: track.original_file_url || undefined,
      loop_verification: verification ? {
        is_seamless: verification.is_seamless,
        verification_score: verification.verification_score || "0.00",
        verification_method: verification.verification_method || undefined,
        verified_at: verification.created_at?.toISOString() || undefined,
        start_analysis: verification.start_analysis || undefined,
        end_analysis: verification.end_analysis || undefined,
      } : null,
    };

    return (
      <>
        {/* Schema.org structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "MusicRecording",
              name: track.title || "AI Music Loop",
              description: track.prompt || "AI-generated seamless music loop",
              duration: `PT${track.duration}S`,
              genre: track.style || "Electronic",
              tempo: track.bpm || undefined,
              inAlbum: {
                "@type": "MusicAlbum",
                name: "LoopCraft AI Generated Loops",
              },
              byArtist: {
                "@type": "MusicGroup",
                name: "LoopCraft AI",
              },
              recordLabel: {
                "@type": "Organization",
                name: "LoopCraft",
                url: process.env.NEXT_PUBLIC_WEB_URL,
              },
              dateCreated: track.created_at,
              url: `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}loops/${slug}`,
              encodingFormat: track.file_format || "audio/mpeg",
              contentSize: track.file_size || undefined,
              isAccessibleForFree: !track.is_premium,
              license: "https://creativecommons.org/licenses/by/4.0/",
              keywords: [
                track.style,
                track.mood,
                "AI music",
                "seamless loop",
                "background music",
                "royalty free",
              ].filter(Boolean).join(", "),
              aggregateRating: verification?.is_seamless ? {
                "@type": "AggregateRating",
                ratingValue: "5",
                bestRating: "5",
                worstRating: "1",
                ratingCount: "1",
                reviewCount: "1",
              } : undefined,
            }),
          }}
        />
        
        <TrackDetailPage track={trackWithVerification} locale={locale} />
      </>
    );
  } catch (error) {
    console.error("Error loading track detail:", error);
    notFound();
  }
}

// Generate static params for popular tracks (optional optimization)
export async function generateStaticParams() {
  // This could be implemented to pre-generate popular track pages
  // For now, we'll use dynamic rendering
  return [];
}
