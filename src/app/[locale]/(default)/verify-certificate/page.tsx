"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Shield, 
  CheckCircle, 
  XCircle, 
  Search,
  Music,
  Clock,
  Calendar,
  FileText
} from "lucide-react";
import { toast } from "sonner";

interface VerificationResult {
  valid: boolean;
  track: {
    uuid: string;
    title: string;
    duration: number;
    format: string;
    style?: string;
    mood?: string;
    bpm?: number;
    created_at: string;
  };
  certificate: {
    id: string;
    verified_at: string;
    status: string;
  };
  message: string;
}

export default function VerifyCertificatePage() {
  const [certificateId, setCertificateId] = useState("");
  const [trackUuid, setTrackUuid] = useState("");
  const [isVerifying, setIsVerifying] = useState(false);
  const [verificationResult, setVerificationResult] = useState<VerificationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleVerify = async () => {
    if (!certificateId.trim() || !trackUuid.trim()) {
      toast.error("Please enter both Certificate ID and Track UUID");
      return;
    }

    setIsVerifying(true);
    setError(null);
    setVerificationResult(null);

    try {
      const response = await fetch("/api/music/certificate/verify", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          certificateId: certificateId.trim(),
          trackUuid: trackUuid.trim(),
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setVerificationResult(data.data);
        toast.success("Certificate verified successfully!");
      } else {
        setError(data.message || "Certificate verification failed");
        toast.error(data.message || "Certificate verification failed");
      }
    } catch (error) {
      console.error("Verification error:", error);
      setError("Network error during verification");
      toast.error("Network error during verification");
    } finally {
      setIsVerifying(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
            <Shield className="h-8 w-8 text-primary" />
          </div>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Certificate Verification
            </h1>
            <p className="text-lg text-muted-foreground mt-2">
              Verify the authenticity of LoopCraft music certificates
            </p>
          </div>
        </div>

        {/* Verification Form */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Enter Certificate Details
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="certificateId">Certificate ID</Label>
              <Input
                id="certificateId"
                placeholder="CERT-XXXXXXXXX"
                value={certificateId}
                onChange={(e) => setCertificateId(e.target.value)}
                className="font-mono"
              />
              <p className="text-xs text-muted-foreground">
                Found at the bottom of your certificate PDF
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="trackUuid">Track UUID</Label>
              <Input
                id="trackUuid"
                placeholder="track-uuid-here"
                value={trackUuid}
                onChange={(e) => setTrackUuid(e.target.value)}
                className="font-mono"
              />
              <p className="text-xs text-muted-foreground">
                The unique identifier of the music track
              </p>
            </div>

            <Button 
              onClick={handleVerify} 
              disabled={isVerifying}
              className="w-full"
            >
              {isVerifying ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                  Verifying...
                </>
              ) : (
                <>
                  <Search className="h-4 w-4 mr-2" />
                  Verify Certificate
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Error Display */}
        {error && (
          <Card className="border-red-200 bg-red-50/50 dark:border-red-800/30 dark:bg-red-950/20">
            <CardContent className="p-6">
              <div className="flex items-center gap-3">
                <XCircle className="h-6 w-6 text-red-600 dark:text-red-400 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-red-800 dark:text-red-200">
                    Verification Failed
                  </h3>
                  <p className="text-red-700 dark:text-red-300 mt-1">
                    {error}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Verification Result */}
        {verificationResult && (
          <Card className="border-green-200 bg-green-50/50 dark:border-green-800/30 dark:bg-green-950/20">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800 dark:text-green-200">
                <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                Certificate Verified
                <Badge variant="default" className="bg-green-600 dark:bg-green-700 ml-auto">
                  Valid
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-green-700 dark:text-green-300">
                {verificationResult.message}
              </p>

              {/* Track Information */}
              <div className="bg-white dark:bg-gray-900/50 p-4 rounded-lg border">
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <Music className="h-4 w-4" />
                  Track Information
                </h4>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Title:</span>
                    <span className="text-sm">{verificationResult.track.title || "Untitled"}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Duration:</span>
                    <span className="text-sm flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {verificationResult.track.duration}s
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Format:</span>
                    <span className="text-sm uppercase">{verificationResult.track.format}</span>
                  </div>

                  {verificationResult.track.style && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Style:</span>
                      <Badge variant="outline">{verificationResult.track.style}</Badge>
                    </div>
                  )}

                  {verificationResult.track.mood && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Mood:</span>
                      <Badge variant="outline">{verificationResult.track.mood}</Badge>
                    </div>
                  )}

                  {verificationResult.track.bpm && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">BPM:</span>
                      <span className="text-sm">{verificationResult.track.bpm}</span>
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Created:</span>
                    <span className="text-sm flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(verificationResult.track.created_at)}
                    </span>
                  </div>
                </div>
              </div>

              {/* Certificate Information */}
              <div className="bg-white dark:bg-gray-900/50 p-4 rounded-lg border">
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Certificate Information
                </h4>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Certificate ID:</span>
                    <span className="text-sm font-mono">{verificationResult.certificate.id}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Verified At:</span>
                    <span className="text-sm">{formatDate(verificationResult.certificate.verified_at)}</span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Status:</span>
                    <Badge variant="default" className="bg-green-600 dark:bg-green-700">
                      {verificationResult.certificate.status}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Information */}
        <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800/30 dark:bg-blue-950/20">
          <CardContent className="p-6">
            <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">
              About Certificate Verification
            </h3>
            <div className="text-blue-700 dark:text-blue-300 text-sm space-y-2">
              <p>
                • Certificates are generated for AI music tracks created on LoopCraft
              </p>
              <p>
                • Each certificate contains a unique ID and track information
              </p>
              <p>
                • Verification confirms the authenticity and ownership of the music
              </p>
              <p>
                • Valid certificates enable commercial use of the music tracks
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}