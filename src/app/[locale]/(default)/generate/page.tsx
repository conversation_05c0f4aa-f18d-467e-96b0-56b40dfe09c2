import { Metadata } from "next";
import MusicGeneratorPage from "@/components/music/generator/music-generator-page";

interface PageProps {
  params: Promise<{
    locale: string;
  }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;
  
  const title = "Generate AI Music Loops - LoopCraft";
  const description = "Create seamless AI-generated music loops for your content. Choose from 15s, 30s, or 60s durations with customizable BPM, style, and mood settings.";

  const canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}generate`;

  return {
    title,
    description,
    keywords: [
      "AI music generator",
      "seamless loops",
      "background music",
      "content creation",
      "royalty free music",
      "music loops",
      "AI composition",
      "custom BPM",
      "music styles",
    ].join(", "),
    openGraph: {
      title,
      description,
      type: "website",
      url: canonicalUrl,
      siteName: "LoopCraft",
      locale: locale,
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/api/og/generate`,
          width: 1200,
          height: 630,
          alt: "Generate AI Music Loops - LoopCraft",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/api/og/generate`],
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function GeneratePage({ params }: PageProps) {
  const { locale } = await params;

  return (
    <>
      {/* Schema.org structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebApplication",
            name: "LoopCraft Music Generator",
            description: "AI-powered seamless music loop generator for content creators",
            url: `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}generate`,
            applicationCategory: "MultimediaApplication",
            operatingSystem: "Web Browser",
            offers: {
              "@type": "Offer",
              price: "0",
              priceCurrency: "USD",
              description: "Free tier with credit-based usage",
            },
            featureList: [
              "AI music generation",
              "Seamless loop creation",
              "Custom BPM control",
              "Multiple music styles",
              "15s, 30s, 60s durations",
              "High-quality audio output",
              "Commercial licensing",
            ],
            creator: {
              "@type": "Organization",
              name: "LoopCraft",
              url: process.env.NEXT_PUBLIC_WEB_URL,
            },
          }),
        }}
      />
      
      <MusicGeneratorPage locale={locale} />
    </>
  );
}
