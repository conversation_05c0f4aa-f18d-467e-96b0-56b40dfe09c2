import { Metadata } from "next";
import { redirect } from "next/navigation";
import { auth } from "@/auth";
import MusicGallery from "@/components/music/gallery/music-gallery";

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;
  
  const title = "My Music Library - LoopCraft";
  const description = "Manage your AI-generated music loops. View, organize, and download your personal collection of seamless music loops.";
  const canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}gallery`;

  return {
    title,
    description,
    keywords: [
      "my music library",
      "personal music collection",
      "AI music loops",
      "music management",
      "download music",
      "music history",
      "generated tracks",
    ].join(", "),
    openGraph: {
      title,
      description,
      type: "website",
      url: canonicalUrl,
      siteName: "LoopCraft",
      locale: locale,
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/api/og/gallery`,
          width: 1200,
          height: 630,
          alt: "My Music Library - LoopCraft",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/api/og/gallery`],
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: false, // Private page, don't index
      follow: false,
    },
  };
}

export default async function GalleryPage({ params }: PageProps) {
  const { locale } = await params;
  
  // Check authentication
  const session = await auth();
  if (!session?.user) {
    redirect(`/${locale}/auth/signin?callbackUrl=/${locale}/gallery`);
  }

  return (
    <>
      {/* Schema.org structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            name: "My Music Library",
            description: "Personal collection of AI-generated music loops",
            url: `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}gallery`,
            isPartOf: {
              "@type": "WebSite",
              name: "LoopCraft",
              url: process.env.NEXT_PUBLIC_WEB_URL,
            },
          }),
        }}
      />
      
      <MusicGallery />
    </>
  );
}
