import { Metadata } from "next";
import ExploreMusic from "@/components/music/explore/explore-music";

interface PageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;
  
  const title = "Explore AI Music Loops - LoopCraft";
  const description = "Discover amazing AI-generated music loops created by our community. Find inspiration and explore different styles, moods, and genres.";
  const canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}explore`;

  return {
    title,
    description,
    keywords: [
      "AI music examples",
      "music loops gallery",
      "discover music",
      "AI generated music",
      "music inspiration",
      "loop samples",
      "background music",
      "royalty free music",
    ].join(", "),
    openGraph: {
      title,
      description,
      type: "website",
      url: canonicalUrl,
      siteName: "LoopCraft",
      locale: locale,
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/api/og/explore`,
          width: 1200,
          height: 630,
          alt: "Explore AI Music Loops - LoopCraft",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/api/og/explore`],
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function ExplorePage({ params }: PageProps) {
  const { locale } = await params;

  return (
    <>
      {/* Schema.org structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            name: "Explore AI Music Loops",
            description: "Discover amazing AI-generated music loops created by our community",
            url: `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}explore`,
            mainEntity: {
              "@type": "MusicPlaylist",
              name: "AI Generated Music Loops",
              description: "Collection of AI-generated seamless music loops",
              genre: ["Electronic", "Ambient", "Corporate", "Cinematic"],
            },
          }),
        }}
      />
      
      <ExploreMusic />
    </>
  );
}
