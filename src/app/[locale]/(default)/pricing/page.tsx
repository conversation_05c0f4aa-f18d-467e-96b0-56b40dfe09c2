import MusicPricing from "@/components/blocks/music-pricing";
import { getPricingPage } from "@/services/page";
import { Metadata } from "next";

interface PageProps {
  params: Promise<{ locale: string }>;
  searchParams: Promise<{ callback?: string }>;
}

export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { locale } = await params;

  const title = "Pricing - LoopCraft AI Music Generator";
  const description = "Create seamless AI music loops with commercial licensing. Start free, upgrade when you need more. Perfect loops, perfect price.";
  const canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}pricing`;

  return {
    title,
    description,
    keywords: [
      "AI music pricing",
      "music generation plans",
      "commercial music license",
      "loop music subscription",
      "AI music generator cost",
      "music creation pricing",
      "seamless loops pricing",
    ].join(", "),
    openGraph: {
      title,
      description,
      type: "website",
      url: canonicalUrl,
      siteName: "LoopCraft",
      locale: locale,
      images: [
        {
          url: `${process.env.NEXT_PUBLIC_WEB_URL}/api/og/pricing`,
          width: 1200,
          height: 630,
          alt: "LoopCraft Pricing Plans",
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title,
      description,
      images: [`${process.env.NEXT_PUBLIC_WEB_URL}/api/og/pricing`],
    },
    alternates: {
      canonical: canonicalUrl,
    },
    robots: {
      index: true,
      follow: true,
    },
  };
}

export default async function PricingPage({ params, searchParams }: PageProps) {
  const { locale } = await params;
  const { callback } = await searchParams;
  const page = await getPricingPage(locale);

  return (
    <>
      {/* Schema.org structured data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPage",
            name: "LoopCraft Pricing",
            description: "AI music generation pricing plans with commercial licensing",
            url: `${process.env.NEXT_PUBLIC_WEB_URL}/${locale === "en" ? "" : locale + "/"}pricing`,
            mainEntity: {
              "@type": "PriceSpecification",
              name: "LoopCraft AI Music Plans",
              description: "Freemium and Professional plans for AI music generation",
              priceCurrency: "USD",
              price: "0-19.99",
            },
          }),
        }}
      />

      {page.pricing && <MusicPricing pricing={page.pricing} callbackUrl={callback} />}
    </>
  );
}
