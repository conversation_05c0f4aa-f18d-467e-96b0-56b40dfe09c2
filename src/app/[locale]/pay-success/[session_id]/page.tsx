import Stripe from "stripe";
import { handleOrderSession } from "@/services/order";
import { redirect } from "next/navigation";

export default async function ({
  params,
}: {
  params: Promise<{ session_id: string }>;
}) {
  let callbackUrl = "";

  try {
    const { session_id } = await params;

    const stripe = new Stripe(process.env.STRIPE_PRIVATE_KEY || "");
    const session = await stripe.checkout.sessions.retrieve(session_id);

    // Extract callback URL from session metadata
    callbackUrl = session.metadata?.callback_url || "";

    await handleOrderSession(session);
  } catch (e) {
    redirect(process.env.NEXT_PUBLIC_PAY_FAIL_URL || "/");
  }

  // Redirect to callback URL if provided, otherwise use default success URL
  const redirectUrl = callbackUrl || process.env.NEXT_PUBLIC_PAY_SUCCESS_URL || "/";
  redirect(redirectUrl);
}
