:root {
  --background: oklch(0.98 0.005 240);
  --foreground: oklch(0.15 0.02 248.51);
  --card: oklch(0.99 0.002 240);
  --card-foreground: oklch(0.15 0.02 248.51);
  --popover: oklch(0.99 0.002 240);
  --popover-foreground: oklch(0.15 0.02 248.51);
  --primary: oklch(0.55 0.25 260); /* Enhanced music purple */
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.45 0.18 320); /* Enhanced magenta */
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.94 0.01 240);
  --muted-foreground: oklch(0.45 0.02 248.51);
  --accent: oklch(0.90 0.12 260); /* Enhanced accent */
  --accent-foreground: oklch(0.15 0.02 248.51);
  --destructive: oklch(0.65 0.26 25.77); /* 提高亮度，增强对比度 */
  --destructive-foreground: oklch(1 0 0);
  --border: oklch(0.90 0.02 240);
  --input: oklch(0.96 0.005 240);
  --ring: oklch(0.55 0.25 260);

  /* Tech-inspired colors */
  --neon-cyan: oklch(0.75 0.15 200);
  --neon-purple: oklch(0.65 0.25 280);
  --neon-pink: oklch(0.70 0.20 320);
  --tech-blue: oklch(0.60 0.20 240);
  --tech-green: oklch(0.65 0.18 140);

  /* Credit status colors - enhanced contrast */
  --credit-sufficient: oklch(0.60 0.20 140); /* 绿色，积分充足 */
  --credit-warning: oklch(0.65 0.25 60); /* 橙色，积分警告 */
  --credit-insufficient: oklch(0.65 0.26 25); /* 红色，积分不足 */
  --chart-1: oklch(0.55 0.22 260); /* Music purple */
  --chart-2: oklch(0.65 0.18 200); /* Audio blue */
  --chart-3: oklch(0.70 0.20 120); /* Sound green */
  --chart-4: oklch(0.60 0.25 40); /* Beat orange */
  --chart-5: oklch(0.45 0.15 320); /* Rhythm magenta */
  --sidebar: oklch(0.98 0 197.14);
  --sidebar-foreground: oklch(0.19 0.01 248.51);
  --sidebar-primary: oklch(0.55 0.22 260);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.92 0.08 260);
  --sidebar-accent-foreground: oklch(0.55 0.22 260);
  --sidebar-border: oklch(0.93 0.01 238.52);
  --sidebar-ring: oklch(0.55 0.22 260);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
}

.dark {
  --background: oklch(0.08 0.01 240);
  --foreground: oklch(0.95 0.005 240);
  --card: oklch(0.12 0.015 240);
  --card-foreground: oklch(0.95 0.005 240);
  --popover: oklch(0.10 0.01 240);
  --popover-foreground: oklch(0.95 0.005 240);
  --primary: oklch(0.70 0.25 260); /* Enhanced music purple for dark */
  --primary-foreground: oklch(0.05 0.01 240);
  --secondary: oklch(0.75 0.18 320); /* Enhanced magenta for dark */
  --secondary-foreground: oklch(0.05 0.01 240);
  --muted: oklch(0.15 0.01 240);
  --muted-foreground: oklch(0.65 0.01 240);
  --accent: oklch(0.20 0.15 260); /* Enhanced accent for dark */
  --accent-foreground: oklch(0.85 0.15 260);
  --destructive: oklch(0.70 0.28 25); /* 深色模式下提高亮度和饱和度 */
  --destructive-foreground: oklch(0.98 0.005 240); /* 提高前景色亮度 */
  --border: oklch(0.25 0.02 240);
  --input: oklch(0.18 0.02 240);
  --ring: oklch(0.70 0.25 260);

  /* Enhanced tech colors for dark mode */
  --neon-cyan: oklch(0.80 0.18 200);
  --neon-purple: oklch(0.75 0.28 280);
  --neon-pink: oklch(0.80 0.22 320);
  --tech-blue: oklch(0.70 0.22 240);
  --tech-green: oklch(0.75 0.20 140);

  /* Credit status colors for dark mode - enhanced visibility */
  --credit-sufficient: oklch(0.70 0.22 140); /* 更亮的绿色 */
  --credit-warning: oklch(0.75 0.28 60); /* 更亮的橙色 */
  --credit-insufficient: oklch(0.75 0.30 25); /* 更亮的红色 */
  --chart-1: oklch(0.65 0.22 260); /* Music purple */
  --chart-2: oklch(0.65 0.18 200); /* Audio blue */
  --chart-3: oklch(0.70 0.20 120); /* Sound green */
  --chart-4: oklch(0.60 0.25 40); /* Beat orange */
  --chart-5: oklch(0.45 0.15 320); /* Rhythm magenta */
  --sidebar: oklch(0.21 0.01 274.53);
  --sidebar-foreground: oklch(0.89 0 0);
  --sidebar-primary: oklch(0.65 0.22 260);
  --sidebar-primary-foreground: oklch(1 0 0);
  --sidebar-accent: oklch(0.25 0.12 260);
  --sidebar-accent-foreground: oklch(0.65 0.22 260);
  --sidebar-border: oklch(0.38 0.02 240.59);
  --sidebar-ring: oklch(0.65 0.22 260);
  --font-sans: Open Sans, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: Menlo, monospace;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 1px 2px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 2px 4px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 4px 6px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0),
    0px 8px 10px -1px hsl(202.82 89.12% 53.14% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.82 89.12% 53.14% / 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  /* Tech-inspired colors */
  --color-neon-cyan: var(--neon-cyan);
  --color-neon-purple: var(--neon-purple);
  --color-neon-pink: var(--neon-pink);
  --color-tech-blue: var(--tech-blue);
  --color-tech-green: var(--tech-green);
}

/* Tech-inspired utility classes */
.glass-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.dark .glass-card {
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.neon-glow {
  box-shadow:
    0 0 5px var(--primary),
    0 0 10px var(--primary),
    0 0 15px var(--primary),
    0 0 20px var(--primary);
}

.tech-gradient {
  background: linear-gradient(135deg,
    var(--primary) 0%,
    var(--neon-purple) 50%,
    var(--neon-cyan) 100%);
}

.dark-tech-gradient {
  background: linear-gradient(135deg,
    oklch(0.05 0.01 240) 0%,
    oklch(0.08 0.015 250) 50%,
    oklch(0.12 0.02 260) 100%);
}

.animated-border {
  position: relative;
  background: linear-gradient(var(--background), var(--background)) padding-box,
              linear-gradient(45deg, var(--primary), var(--neon-cyan), var(--neon-purple)) border-box;
  border: 2px solid transparent;
}

.pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite alternate;
}

@keyframes pulse-glow {
  from {
    box-shadow: 0 0 5px var(--primary);
  }
  to {
    box-shadow: 0 0 20px var(--primary), 0 0 30px var(--primary);
  }
}

.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.tech-grid {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

.dark .tech-grid {
  background-image:
    linear-gradient(rgba(255, 255, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.02) 1px, transparent 1px);
}
