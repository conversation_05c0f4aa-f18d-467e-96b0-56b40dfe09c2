import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { searchTracks, getAvailableStyles, getAvailableMoods } from "@/models/track";
import { GetTracksRequest, GetTracksResponse } from "@/types/music-api";
import { TrackSearchParams } from "@/types/music";

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    
    // Parse query parameters
    const searchParams: TrackSearchParams = {
      query: url.searchParams.get("search") || undefined,
      style: url.searchParams.get("style") || undefined,
      mood: url.searchParams.get("mood") || undefined,
      bpm_min: url.searchParams.get("bpm_min") ? parseInt(url.searchParams.get("bpm_min")!) : undefined,
      bpm_max: url.searchParams.get("bpm_max") ? parseInt(url.searchParams.get("bpm_max")!) : undefined,
      duration: url.searchParams.get("duration") ? parseInt(url.searchParams.get("duration")!) : undefined,
      user_uuid: url.searchParams.get("user_uuid") || undefined,
      is_public: url.searchParams.get("is_public") ? url.searchParams.get("is_public") === "true" : undefined,
      sort_by: url.searchParams.get("sort_by") as any || "created_at",
      sort_order: url.searchParams.get("sort_order") as "asc" | "desc" || "desc",
      page: url.searchParams.get("page") ? parseInt(url.searchParams.get("page")!) : 1,
      per_page: url.searchParams.get("per_page") ? parseInt(url.searchParams.get("per_page")!) : 20,
    };

    // Validate pagination
    if (searchParams.page! < 1) {
      return respErr("Page must be >= 1");
    }
    if (searchParams.per_page! < 1 || searchParams.per_page! > 100) {
      return respErr("Per page must be between 1 and 100");
    }

    // Get current user (optional for public tracks)
    const current_user_uuid = await getUserUuid();

    // If no user_uuid specified and user is authenticated, default to public tracks
    // If user_uuid is specified, check permissions
    if (searchParams.user_uuid && searchParams.user_uuid !== current_user_uuid) {
      // Viewing another user's tracks - only show public ones
      searchParams.is_public = true;
    } else if (!searchParams.user_uuid && !current_user_uuid) {
      // Anonymous user - only show public tracks
      searchParams.is_public = true;
    }

    // Search tracks
    const { tracks, total } = await searchTracks(searchParams);

    // Calculate pagination info
    const total_pages = Math.ceil(total / searchParams.per_page!);
    const has_more = searchParams.page! < total_pages;

    // Get filter options for the response
    const [available_styles, available_moods] = await Promise.all([
      getAvailableStyles(),
      getAvailableMoods(),
    ]);

    const response: GetTracksResponse = {
      code: 0,
      message: "ok",
      data: {
        tracks,
        pagination: {
          total,
          page: searchParams.page!,
          per_page: searchParams.per_page!,
          total_pages,
          has_more,
        },
        filters: {
          available_styles,
          available_moods,
          bpm_range: { min: 60, max: 200 }, // Standard BPM range
        },
      },
    };

    return respData(response.data);
  } catch (error) {
    console.error("Get tracks error:", error);
    return respErr("Failed to get tracks");
  }
}

export async function POST(req: Request) {
  try {
    const body: GetTracksRequest = await req.json();
    
    // Convert request to search params
    const searchParams: TrackSearchParams = {
      query: body.search,
      style: body.style,
      mood: body.mood,
      bpm_min: body.bpm_min,
      bpm_max: body.bpm_max,
      duration: body.duration,
      user_uuid: body.user_uuid,
      is_public: body.is_public,
      sort_by: body.sort_by || "created_at",
      sort_order: body.sort_order || "desc",
      page: body.page || 1,
      per_page: body.per_page || 20,
    };

    // Validate pagination
    if (searchParams.page! < 1) {
      return respErr("Page must be >= 1");
    }
    if (searchParams.per_page! < 1 || searchParams.per_page! > 100) {
      return respErr("Per page must be between 1 and 100");
    }

    // Get current user
    const current_user_uuid = await getUserUuid();

    // Permission checks
    if (searchParams.user_uuid && searchParams.user_uuid !== current_user_uuid) {
      searchParams.is_public = true;
    } else if (!searchParams.user_uuid && !current_user_uuid) {
      searchParams.is_public = true;
    }

    // Search tracks
    const { tracks, total } = await searchTracks(searchParams);

    // Calculate pagination
    const total_pages = Math.ceil(total / searchParams.per_page!);
    const has_more = searchParams.page! < total_pages;

    // Get filter options
    const [available_styles, available_moods] = await Promise.all([
      getAvailableStyles(),
      getAvailableMoods(),
    ]);

    const response: GetTracksResponse = {
      code: 0,
      message: "ok",
      data: {
        tracks,
        pagination: {
          total,
          page: searchParams.page!,
          per_page: searchParams.per_page!,
          total_pages,
          has_more,
        },
        filters: {
          available_styles,
          available_moods,
          bpm_range: { min: 60, max: 200 },
        },
      },
    };

    return respData(response.data);
  } catch (error) {
    console.error("Search tracks error:", error);
    return respErr("Failed to search tracks");
  }
}
