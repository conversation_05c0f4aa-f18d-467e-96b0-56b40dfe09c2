import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findTrackByUuid } from "@/models/track";
import { db } from "@/db";
import { tracks } from "@/db/schema";
import { eq } from "drizzle-orm";

export async function PATCH(
  req: Request,
  { params }: { params: Promise<{ uuid: string }> }
) {
  try {
    const { uuid } = await params;
    const body = await req.json();
    const { is_public } = body;

    // Validate input
    if (typeof is_public !== "boolean") {
      return respErr("Invalid visibility value");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check if user owns the track
    if (track.user_uuid !== user_uuid) {
      return respErr("Permission denied");
    }

    // TODO: Check if user has premium access
    // For now, we'll allow all authenticated users
    // In production, you should check user's subscription status

    // Update track visibility
    await db()
      .update(tracks)
      .set({ 
        is_public,
        updated_at: new Date(),
      })
      .where(eq(tracks.uuid, uuid));

    console.log("Track visibility updated:", {
      track_uuid: uuid,
      user_uuid,
      is_public,
    });

    return respData({
      success: true,
      is_public,
      message: `Track is now ${is_public ? "public" : "private"}`,
    });

  } catch (error) {
    console.error("Visibility update error:", error);
    return respErr("Failed to update track visibility");
  }
}