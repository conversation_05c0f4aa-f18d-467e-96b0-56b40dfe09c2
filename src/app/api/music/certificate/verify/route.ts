import { NextRequest } from "next/server";
import { respData, respErr } from "@/lib/resp";
import { findTrackByUuid } from "@/models/track";

export async function GET(req: NextRequest) {
  try {
    const searchParams = req.nextUrl.searchParams;
    const certificateId = searchParams.get("id");
    const trackUuid = searchParams.get("track");

    if (!certificateId || !trackUuid) {
      return respErr("Certificate ID and track UUID are required");
    }

    // Validate certificate ID format
    if (!certificateId.startsWith("CERT-")) {
      return respErr("Invalid certificate ID format");
    }

    // Find the track
    const track = await findTrackByUuid(trackUuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Extract timestamp from certificate ID
    const certParts = certificateId.split("-");
    if (certParts.length < 2) {
      return respErr("Invalid certificate ID format");
    }

    const certTimestamp = certParts[1];
    
    // Basic validation - certificate should be generated after track creation
    const trackCreatedTime = track.created_at ? new Date(track.created_at).getTime() : 0;
    const certTime = parseInt(certTimestamp, 36);
    
    if (certTime < trackCreatedTime) {
      return respErr("Invalid certificate - predates track creation");
    }

    // Return verification result
    return respData({
      valid: true,
      track: {
        uuid: track.uuid,
        title: track.title,
        duration: track.duration,
        format: track.file_format,
        style: track.style,
        mood: track.mood,
        bpm: track.bpm,
        created_at: track.created_at,
      },
      certificate: {
        id: certificateId,
        verified_at: new Date().toISOString(),
        status: "valid",
      },
      message: "Certificate is valid and authentic",
    });

  } catch (error) {
    console.error("Certificate verification error:", error);
    return respErr("Certificate verification failed");
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { certificateId, trackUuid } = body;

    if (!certificateId || !trackUuid) {
      return respErr("Certificate ID and track UUID are required");
    }

    // Same verification logic as GET
    const searchParams = new URLSearchParams({
      id: certificateId,
      track: trackUuid,
    });

    // Reuse GET logic
    const verifyReq = new NextRequest(
      `${req.nextUrl.origin}/api/music/certificate/verify?${searchParams}`,
      { method: "GET" }
    );

    return GET(verifyReq);

  } catch (error) {
    console.error("Certificate verification error:", error);
    return respErr("Certificate verification failed");
  }
}