/**
 * 手动触发音乐生成状态检查的测试 API
 */

import { respData, respErr } from "@/lib/resp";
import { findPendingMusicGenerations, updateMusicGenerationStatus } from "@/models/music-generation";
import { findTrackByGenerationUuid } from "@/models/track";
import { MusicProviderFactory } from "@/services/music-provider";
import { TrackCreationService } from "@/services/track-creation-service";
import { randomUUID } from "crypto";

export async function POST(req: Request) {
  try {
    console.log("Manual status check triggered");

    // 获取所有进行中的生成任务
    const pendingGenerations = await findPendingMusicGenerations();
    
    if (pendingGenerations.length === 0) {
      return respData({
        message: "No pending music generations to check",
        checked: 0,
        updated: 0
      });
    }

    console.log(`Found ${pendingGenerations.length} pending music generations`);

    // 确保提供商已初始化
    await MusicProviderFactory.initialize();

    let updatedCount = 0;

    // 处理每个任务
    for (const generation of pendingGenerations) {
      try {
        console.log(`Checking generation ${generation.uuid} (task: ${generation.provider_task_id})`);

        // 检查是否有 provider_task_id
        if (!generation.provider_task_id) {
          console.log(`No provider_task_id for generation ${generation.uuid}, skipping`);
          continue;
        }

        // 获取提供商实例
        const provider = MusicProviderFactory.getProvider(generation.provider || "volcano");
        if (!provider) {
          console.error(`Provider not found: ${generation.provider}`);
          continue;
        }

        // 查询提供商状态
        const providerStatus = await provider.checkStatus(generation.provider_task_id);
        console.log(`Provider status for ${generation.uuid}:`, providerStatus);

        // 如果状态没有变化，跳过更新
        if (providerStatus.status === generation.status) {
          console.log(`Status unchanged for ${generation.uuid}: ${providerStatus.status}`);
          continue;
        }

        // 更新数据库状态
        await updateMusicGenerationStatus(
          generation.uuid,
          providerStatus.status,
          providerStatus.error
        );

        console.log(`Updated generation ${generation.uuid} status: ${generation.status} -> ${providerStatus.status}`);
        updatedCount++;

        // 如果完成了，创建音轨记录
        if (providerStatus.status === "completed" && providerStatus.result) {
          const existingTrack = await findTrackByGenerationUuid(generation.uuid);
          if (!existingTrack) {
            // 使用统一的track创建服务，正确处理premium和水印
            const track = await TrackCreationService.createTrack({
              generation,
              file_url: providerStatus.result.file_url,
              file_size: providerStatus.result.file_size || 0,
              file_format: "mp3",
              metadata: providerStatus.result.metadata || {},
              is_public: true, // 默认公开
            });

            console.log(`Created track for generation ${generation.uuid}:`, track?.uuid);
          }
        }

      } catch (error) {
        console.error(`Failed to update generation ${generation.uuid}:`, error);
      }
    }

    return respData({
      message: "Status check completed",
      checked: pendingGenerations.length,
      updated: updatedCount,
      generations: pendingGenerations.map(g => ({
        uuid: g.uuid,
        provider_task_id: g.provider_task_id,
        status: g.status,
        provider: g.provider
      }))
    });

  } catch (error) {
    console.error("Manual status check error:", error);
    return respErr("Failed to check music generation statuses");
  }
}

export async function GET(req: Request) {
  // GET 方法也支持手动检查
  return POST(req);
}
