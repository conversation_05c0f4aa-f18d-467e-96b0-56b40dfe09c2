import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findTrackByUuid } from "@/models/track";
import { insertTrackVariation, getTrackVariations } from "@/models/track-variation";
import { getUuid } from "@/lib/hash";
import { Track } from "@/types/music";

interface GenerateVariationRequest {
  original_track_uuid: string;
  variation_type: "tempo" | "style" | "mood" | "key" | "arrangement";
  variation_params?: {
    tempo_change?: number; // -50 to +50 BPM
    new_style?: string;
    new_mood?: string;
    key_shift?: number; // semitones
    arrangement_type?: "minimal" | "full" | "acoustic" | "electronic";
  };
  quality?: "standard" | "high";
}

interface GenerateVariationResponse {
  code: number;
  message: string;
  data: {
    variation_uuid: string;
    original_track_uuid: string;
    variation_type: string;
    generation_id: string;
    estimated_time: number;
    status: "pending" | "processing" | "completed" | "failed";
  };
}

export async function POST(req: Request) {
  try {
    const body: GenerateVariationRequest = await req.json();
    const { 
      original_track_uuid, 
      variation_type, 
      variation_params = {},
      quality = "standard"
    } = body;

    if (!original_track_uuid || !variation_type) {
      return respErr("Missing required parameters: original_track_uuid and variation_type");
    }

    // Validate variation type
    const validTypes = ["tempo", "style", "mood", "key", "arrangement"];
    if (!validTypes.includes(variation_type)) {
      return respErr(`Invalid variation_type. Must be one of: ${validTypes.join(", ")}`);
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the original track
    const originalTrack = await findTrackByUuid(original_track_uuid);
    if (!originalTrack) {
      return respErr("Original track not found");
    }

    // Check if user owns the track or if it's public
    if (originalTrack.user_uuid !== user_uuid && !originalTrack.is_public) {
      return respErr("Access denied");
    }

    // Validate variation parameters
    const validationResult = validateVariationParams(variation_type, variation_params);
    if (!validationResult.valid) {
      return respErr(validationResult.error || "Invalid variation parameters");
    }

    // Generate unique UUIDs
    const variation_uuid = getUuid();
    const generation_id = getUuid();

    // Create variation record
    const variationData = {
      uuid: variation_uuid,
      original_track_uuid,
      user_uuid,
      variation_type,
      variation_params: variation_params as any,
      file_url: "", // Will be updated when generation completes
      file_size: 0,
      file_format: "mp3",
      created_at: new Date(),
    };

    const variation = await insertTrackVariation(variationData);
    if (!variation) {
      return respErr("Failed to create variation record");
    }

    // Start variation generation process
    console.log("Starting variation generation:", {
      variation_uuid,
      original_track_uuid,
      variation_type,
      variation_params,
      quality,
      user_uuid,
    });

    try {
      const { VariationGenerationService } = await import("@/services/variation-generation");
      
      // Start the generation process asynchronously
      VariationGenerationService.generateVariation(
        {
          ...originalTrack,
          title: originalTrack.title || undefined,
          slug: originalTrack.slug || undefined,
          style: originalTrack.style || undefined,
          mood: originalTrack.mood || undefined,
          bpm: originalTrack.bpm || undefined,
          file_size: originalTrack.file_size || undefined,
          waveform_data: originalTrack.waveform_data as { peaks: number[]; duration: number; sample_rate: number; } | undefined,
          metadata: originalTrack.metadata || undefined,
          created_at: originalTrack.created_at?.toISOString() || undefined,
          updated_at: originalTrack.updated_at?.toISOString() || undefined,
        } as Track,
        variation_type,
        variation_params,
        {
          quality,
          generation_id,
          variation_uuid,
          user_uuid,
        }
      ).catch(error => {
        console.error("Variation generation failed:", error);
      });

      // Estimate completion time based on variation type and track duration
      const estimated_time = estimateVariationTime(variation_type, originalTrack.duration, quality);

      const response: GenerateVariationResponse = {
        code: 0,
        message: "Variation generation started",
        data: {
          variation_uuid,
          original_track_uuid,
          variation_type,
          generation_id,
          estimated_time,
          status: "pending",
        },
      };

      return respData(response.data);
    } catch (error) {
      console.error("Failed to start variation generation:", error);
      return respErr("Failed to start variation generation");
    }
  } catch (error) {
    console.error("Variation generation API error:", error);
    return respErr("Failed to process variation generation request");
  }
}

// GET method to check generation status or list variations
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const original_track_uuid = url.searchParams.get("original_track_uuid");
    const variation_uuid = url.searchParams.get("variation_uuid");
    const generation_id = url.searchParams.get("generation_id");

    if (!original_track_uuid && !variation_uuid) {
      return respErr("Either original_track_uuid or variation_uuid is required");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    if (variation_uuid) {
      // Get specific variation status
      const { findTrackVariationByUuid } = await import("@/models/track-variation");
      const variation = await findTrackVariationByUuid(variation_uuid);
      
      if (!variation) {
        return respErr("Variation not found");
      }

      // Check access permissions
      if (variation.user_uuid !== user_uuid) {
        return respErr("Access denied");
      }

      return respData({
        variation_uuid,
        original_track_uuid: variation.original_track_uuid,
        variation_type: variation.variation_type,
        status: variation.file_url ? "completed" : "pending",
        file_url: variation.file_url || null,
        file_size: variation.file_size || null,
        created_at: variation.created_at,
      });
    } else if (original_track_uuid) {
      // List all variations for a track
      const originalTrack = await findTrackByUuid(original_track_uuid);
      if (!originalTrack) {
        return respErr("Original track not found");
      }

      // Check access permissions
      if (originalTrack.user_uuid !== user_uuid && !originalTrack.is_public) {
        return respErr("Access denied");
      }

      const variations = await getTrackVariations(original_track_uuid);

      return respData({
        original_track_uuid,
        variations: variations.map(v => ({
          uuid: v.uuid,
          variation_type: v.variation_type,
          variation_params: v.variation_params,
          file_url: v.file_url,
          file_size: v.file_size,
          file_format: v.file_format,
          created_at: v.created_at,
          status: v.file_url ? "completed" : "pending",
        })),
      });
    }

    return respErr("Invalid request");
  } catch (error) {
    console.error("Variation status API error:", error);
    return respErr("Failed to get variation status");
  }
}

/**
 * Validate variation parameters based on type
 */
function validateVariationParams(
  variation_type: string,
  params: any
): { valid: boolean; error?: string } {
  switch (variation_type) {
    case "tempo":
      if (params.tempo_change !== undefined) {
        if (typeof params.tempo_change !== "number" || 
            params.tempo_change < -50 || 
            params.tempo_change > 50) {
          return { valid: false, error: "tempo_change must be between -50 and +50 BPM" };
        }
      }
      break;
    
    case "style":
      if (params.new_style !== undefined) {
        if (typeof params.new_style !== "string" || params.new_style.length === 0) {
          return { valid: false, error: "new_style must be a non-empty string" };
        }
      }
      break;
    
    case "mood":
      if (params.new_mood !== undefined) {
        if (typeof params.new_mood !== "string" || params.new_mood.length === 0) {
          return { valid: false, error: "new_mood must be a non-empty string" };
        }
      }
      break;
    
    case "key":
      if (params.key_shift !== undefined) {
        if (typeof params.key_shift !== "number" || 
            params.key_shift < -12 || 
            params.key_shift > 12) {
          return { valid: false, error: "key_shift must be between -12 and +12 semitones" };
        }
      }
      break;
    
    case "arrangement":
      if (params.arrangement_type !== undefined) {
        const validArrangements = ["minimal", "full", "acoustic", "electronic"];
        if (!validArrangements.includes(params.arrangement_type)) {
          return { valid: false, error: `arrangement_type must be one of: ${validArrangements.join(", ")}` };
        }
      }
      break;
  }

  return { valid: true };
}

/**
 * Estimate variation generation time
 */
function estimateVariationTime(
  variation_type: string,
  duration: number,
  quality: string
): number {
  const baseTime = duration * 1.5; // 1.5 seconds per second of audio
  
  const typeMultipliers = {
    tempo: 1.0,
    style: 2.0,
    mood: 1.5,
    key: 1.2,
    arrangement: 2.5,
  };
  
  const typeMultiplier = typeMultipliers[variation_type as keyof typeof typeMultipliers] || 1.5;
  const qualityMultiplier = quality === "high" ? 1.5 : 1;
  
  return Math.ceil(baseTime * typeMultiplier * qualityMultiplier);
}
