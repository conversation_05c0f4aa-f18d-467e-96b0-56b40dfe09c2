import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findTrackByUuid, updateTrack } from "@/models/track";
import { AudioAnalysisService } from "@/services/audio-analysis";

export async function POST(req: Request) {
  try {
    const { track_uuid, analysis_type = "full" } = await req.json();

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Validate analysis type
    const validTypes = ["full", "bpm", "key", "waveform"];
    if (!validTypes.includes(analysis_type)) {
      return respErr(`Invalid analysis_type. Must be one of: ${validTypes.join(", ")}`);
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check access permissions
    if (track.user_uuid !== user_uuid && !track.is_public) {
      return respErr("Access denied");
    }

    console.log(`Starting ${analysis_type} analysis for track: ${track_uuid}`);

    const analysisResults: any = {
      track_uuid,
      analysis_type,
    };

    // Perform requested analysis
    switch (analysis_type) {
      case "bpm":
        analysisResults.bpm = await AudioAnalysisService.detectBPM(track.file_url);
        break;

      case "key":
        analysisResults.key = await AudioAnalysisService.detectKey(track.file_url);
        break;

      case "waveform":
        analysisResults.waveform_data = await AudioAnalysisService.generateWaveform(track.file_url);
        break;

      case "full":
        // Perform all analyses
        const [bpm, key, waveformData] = await Promise.all([
          AudioAnalysisService.detectBPM(track.file_url),
          AudioAnalysisService.detectKey(track.file_url),
          AudioAnalysisService.generateWaveform(track.file_url),
        ]);

        analysisResults.bpm = bpm;
        analysisResults.key = key;
        analysisResults.waveform_data = waveformData;
        break;
    }

    // Update track metadata with analysis results
    const updateData: any = {
      updated_at: new Date(),
    };

    if (analysisResults.bpm && !track.bpm) {
      updateData.bpm = analysisResults.bpm;
    }

    if (analysisResults.key && !track.key_signature) {
      updateData.key_signature = analysisResults.key;
    }

    if (analysisResults.waveform_data && !track.waveform_data) {
      updateData.waveform_data = analysisResults.waveform_data;
    }

    // Update metadata
    if (analysisResults.bpm || analysisResults.key) {
      const currentMetadata = track.metadata || {};
      updateData.metadata = {
        ...currentMetadata,
        ...(analysisResults.bpm && { bpm: analysisResults.bpm }),
        ...(analysisResults.key && { key: analysisResults.key }),
        analyzed_at: new Date().toISOString(),
      };
    }

    if (Object.keys(updateData).length > 1) { // More than just updated_at
      await updateTrack(track_uuid, updateData);
    }

    return respData(analysisResults);
  } catch (error) {
    console.error("Audio analysis error:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return respErr(`Audio analysis failed: ${errorMessage}`);
  }
}

// GET method for getting existing analysis data
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const track_uuid = url.searchParams.get("track_uuid");

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Get user authentication (optional for public tracks)
    const user_uuid = await getUserUuid();

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check access permissions
    if (!track.is_public && track.user_uuid !== user_uuid) {
      return respErr("Access denied");
    }

    // Return existing analysis data
    const analysisData = {
      track_uuid,
      bpm: track.bpm,
      key: track.key_signature,
      waveform_data: track.waveform_data,
      metadata: track.metadata,
      has_analysis: !!(track.bpm || track.key_signature || track.waveform_data),
    };

    return respData(analysisData);
  } catch (error) {
    console.error("Get analysis error:", error);
    return respErr("Failed to get analysis data");
  }
}
