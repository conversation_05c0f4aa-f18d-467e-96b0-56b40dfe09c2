import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { getUserCredits } from "@/services/credit";
import { findTrackByUuid, incrementTrackDownloadCount } from "@/models/track";
import { newStorage } from "@/lib/storage";
import { DownloadMusicRequest, DownloadMusicResponse } from "@/types/music-api";

export async function POST(req: Request) {
  try {
    const body: DownloadMusicRequest = await req.json();
    const { track_uuid, format = "mp3", quality = "standard" } = body;

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Validate format
    if (!["mp3", "wav"].includes(format)) {
      return respErr("Format must be mp3 or wav");
    }

    // Validate quality
    if (!["standard", "high"].includes(quality)) {
      return respErr("Quality must be standard or high");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check access permissions
    const userCredits = await getUserCredits(user_uuid);
    const isOwner = track.user_uuid === user_uuid;
    const isPublic = track.is_public;
    const isPro = userCredits.is_pro;

    // Access control logic
    if (!isOwner && !isPublic) {
      return respErr("Access denied - track is private");
    }

    // Format restrictions for non-pro users
    if (!isPro && !isOwner) {
      if (format === "wav") {
        return respErr("WAV format requires Pro subscription");
      }
      if (quality === "high") {
        return respErr("High quality requires Pro subscription");
      }
    }

    // Generate download URL
    let file_url = track.file_url;
    
    // If requesting different format or quality, we might need to convert
    // For now, we'll use the original file URL
    if (format !== track.file_format) {
      // TODO: Implement format conversion
      console.log(`Format conversion requested: ${track.file_format} -> ${format}`);
    }

    // Generate signed URL for download (expires in 1 hour)
    const storage = newStorage();
    let download_url: string;
    let expires_at: Date;

    try {
      // Extract key from file URL
      const urlParts = file_url.split('/');
      const key = urlParts.slice(-2).join('/'); // Get last two parts as key

      expires_at = new Date(Date.now() + 60 * 60 * 1000); // 1 hour from now
      
      // Generate presigned URL
      download_url = await storage.getSignedUrl(key, 3600); // 1 hour expiry
    } catch (error) {
      console.error("Failed to generate download URL:", error);
      return respErr("Failed to generate download URL");
    }

    // Increment download count (async, don't wait)
    incrementTrackDownloadCount(track_uuid).catch(error => {
      console.error("Failed to increment download count:", error);
    });

    // For non-pro users downloading public tracks, add watermark info
    let watermark_info = undefined;
    if (!isPro && !isOwner && isPublic) {
      watermark_info = {
        has_watermark: true,
        watermark_text: "Generated by LoopCraft.app",
        usage_restriction: "Personal use only",
      };
    }

    const response: DownloadMusicResponse = {
      code: 0,
      message: "Download URL generated",
      data: {
        download_url,
        expires_at: expires_at.toISOString(),
        file_size: track.file_size || 0,
        format: format,
        ...watermark_info,
      },
    };

    return respData(response.data);
  } catch (error) {
    console.error("Music download error:", error);
    return respErr("Failed to generate download URL");
  }
}

// GET method for download
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const track_uuid = url.searchParams.get("track_uuid");
    const format = url.searchParams.get("format") || "mp3";
    const quality = url.searchParams.get("quality") || "standard";

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Validate parameters
    if (!["mp3", "wav"].includes(format)) {
      return respErr("Format must be mp3 or wav");
    }

    if (!["standard", "high"].includes(quality)) {
      return respErr("Quality must be standard or high");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check access permissions
    const userCredits = await getUserCredits(user_uuid);
    const isOwner = track.user_uuid === user_uuid;
    const isPublic = track.is_public;
    const isPro = userCredits.is_pro;

    if (!isOwner && !isPublic) {
      return respErr("Access denied - track is private");
    }

    if (!isPro && !isOwner) {
      if (format === "wav") {
        return respErr("WAV format requires Pro subscription");
      }
      if (quality === "high") {
        return respErr("High quality requires Pro subscription");
      }
    }

    // Generate download URL
    const storage = newStorage();
    let download_url: string;
    const expires_at = new Date(Date.now() + 60 * 60 * 1000);

    try {
      const urlParts = track.file_url.split('/');
      const key = urlParts.slice(-2).join('/');
      download_url = await storage.getSignedUrl(key, 3600);
    } catch (error) {
      console.error("Failed to generate download URL:", error);
      return respErr("Failed to generate download URL");
    }

    // Increment download count
    incrementTrackDownloadCount(track_uuid).catch(error => {
      console.error("Failed to increment download count:", error);
    });

    return respData({
      download_url,
      expires_at: expires_at.toISOString(),
      file_size: track.file_size || 0,
      format: format,
    });
  } catch (error) {
    console.error("Music download error:", error);
    return respErr("Failed to generate download URL");
  }
}
