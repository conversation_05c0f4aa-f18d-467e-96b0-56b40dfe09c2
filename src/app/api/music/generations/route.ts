import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/auth";
import { getUserGenerations } from "@/models/music-generation";
import { findTrackByGenerationUuid } from "@/models/track";

export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.uuid) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const offset = parseInt(searchParams.get("offset") || "0");
    const status = searchParams.get("status");

    const generations = await getUserGenerations(
      session.user.uuid,
      limit,
      offset,
      status as any
    );

    // 为每个已完成的生成记录检查并创建对应的 track 记录
    const generationsWithTracks = await Promise.all(
      generations.map(async (generation) => {
        if (generation.status === "completed") {
          // 检查是否已有对应的 track 记录
          let track = await findTrackByGenerationUuid(generation.uuid);

          if (!track) {
            // 对于pending状态的generation，不创建占位符track
            // track会在音乐生成完成时通过TrackCreationService创建
            console.log(`No track found for pending generation ${generation.uuid}, will be created when completed`);
          }

          // 将 track 信息合并到 generation 中
          return {
            ...generation,
            track: track
          };
        }

        return generation;
      })
    );

    return NextResponse.json({
      generations: generationsWithTracks,
      success: true
    });
  } catch (error) {
    console.error("Failed to get user generations:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
