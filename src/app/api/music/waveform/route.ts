import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findTrackByUuid } from "@/models/track";
import { AudioAnalysisService } from "@/services/audio-analysis";

export async function POST(req: Request) {
  try {
    const { track_uuid } = await req.json();

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check access permissions
    if (track.user_uuid !== user_uuid && !track.is_public) {
      return respErr("Access denied");
    }

    // Check if waveform data already exists
    if (track.waveform_data) {
      return respData({
        track_uuid,
        waveform_data: track.waveform_data,
        cached: true,
      });
    }

    // Generate waveform data
    console.log(`Generating waveform for track: ${track_uuid}`);
    
    const waveformData = await AudioAnalysisService.generateWaveform(track.file_url);

    // Update track with waveform data
    const { updateTrack } = await import("@/models/track");
    await updateTrack(track_uuid, {
      waveform_data: waveformData,
      updated_at: new Date(),
    });

    return respData({
      track_uuid,
      waveform_data: waveformData,
      cached: false,
    });
  } catch (error) {
    console.error("Waveform generation error:", error);
    return respErr("Failed to generate waveform");
  }
}

// GET method for waveform data
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const track_uuid = url.searchParams.get("track_uuid");

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Get user authentication (optional for public tracks)
    const user_uuid = await getUserUuid();

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check access permissions
    if (!track.is_public && track.user_uuid !== user_uuid) {
      return respErr("Access denied");
    }

    // Return existing waveform data if available
    if (track.waveform_data) {
      return respData({
        track_uuid,
        waveform_data: track.waveform_data,
        cached: true,
      });
    }

    // Generate waveform data if not cached
    try {
      const waveformData = await AudioAnalysisService.generateWaveform(track.file_url);

      // Update track with waveform data
      const { updateTrack } = await import("@/models/track");
      await updateTrack(track_uuid, {
        waveform_data: waveformData,
        updated_at: new Date(),
      });

      return respData({
        track_uuid,
        waveform_data: waveformData,
        cached: false,
      });
    } catch (error) {
      console.error("Waveform generation failed:", error);
      return respErr("Failed to generate waveform");
    }
  } catch (error) {
    console.error("Waveform API error:", error);
    return respErr("Failed to get waveform data");
  }
}
