import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findTrackByUuid } from "@/models/track";
import { insertLoopVerification, findLoopVerificationByTrackUuid } from "@/models/loop-verification";
import { VerifyLoopRequest, VerifyLoopResponse } from "@/types/music-api";
import { AudioAnalysis } from "@/types/music";



export async function POST(req: Request) {
  try {
    const body: VerifyLoopRequest = await req.json();
    const { track_uuid, method = "ml" } = body;

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Validate method
    if (!["ml", "signal_processing"].includes(method)) {
      return respErr("Method must be 'ml' or 'signal_processing'");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check if user owns the track or if it's public
    if (track.user_uuid !== user_uuid && !track.is_public) {
      return respErr("Access denied");
    }

    // Check if verification already exists (within last 24 hours)
    const existingVerification = await findLoopVerificationByTrackUuid(track_uuid);
    if (existingVerification) {
      const hoursSinceVerification = (Date.now() - new Date(existingVerification.created_at!).getTime()) / (1000 * 60 * 60);
      
      if (hoursSinceVerification < 24) {
        // Return existing verification
        const response: VerifyLoopResponse = {
          code: 0,
          message: "Using cached verification result",
          data: {
            track_uuid,
            is_seamless: existingVerification.is_seamless,
            verification_score: Number(existingVerification.verification_score || 0),
            analysis_details: {
              start_analysis: existingVerification.start_analysis as AudioAnalysis,
              end_analysis: existingVerification.end_analysis as AudioAnalysis,
              similarity_score: Number(existingVerification.verification_score || 0),
            },
          },
        };

        return respData(response.data);
      }
    }

    // Perform audio analysis
    console.log(`Starting loop verification for track ${track_uuid} using ${method} method`);

    const { AudioAnalysisService } = await import("@/services/audio-analysis");
    const analysisResult = await AudioAnalysisService.analyzeAudioLoop(track.file_url, method);

    // Store verification result
    const verificationData = {
      track_uuid,
      verification_score: analysisResult.verification_score.toString(),
      is_seamless: analysisResult.is_seamless,
      start_analysis: analysisResult.start_analysis,
      end_analysis: analysisResult.end_analysis,
      verification_method: method,
      created_at: new Date(),
    };

    const verification = await insertLoopVerification(verificationData);
    if (!verification) {
      return respErr("Failed to store verification result");
    }

    const response: VerifyLoopResponse = {
      code: 0,
      message: "Loop verification completed",
      data: {
        track_uuid,
        is_seamless: analysisResult.is_seamless,
        verification_score: analysisResult.verification_score,
        analysis_details: {
          start_analysis: analysisResult.start_analysis,
          end_analysis: analysisResult.end_analysis,
          similarity_score: analysisResult.verification_score,
        },
      },
    };

    return respData(response.data);
  } catch (error) {
    console.error("Loop verification error:", error);
    return respErr("Failed to verify loop");
  }
}

// GET method for getting verification status
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const track_uuid = url.searchParams.get("track_uuid");

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check access permissions
    if (track.user_uuid !== user_uuid && !track.is_public) {
      return respErr("Access denied");
    }

    // Get existing verification
    const verification = await findLoopVerificationByTrackUuid(track_uuid);
    if (!verification) {
      return respData({
        track_uuid,
        has_verification: false,
        message: "No verification found for this track",
      });
    }

    return respData({
      track_uuid,
      has_verification: true,
      is_seamless: verification.is_seamless,
      verification_score: Number(verification.verification_score || 0),
      verification_method: verification.verification_method,
      verified_at: verification.created_at,
      analysis_details: {
        start_analysis: verification.start_analysis as AudioAnalysis,
        end_analysis: verification.end_analysis as AudioAnalysis,
        similarity_score: Number(verification.verification_score || 0),
      },
    });
  } catch (error) {
    console.error("Get verification error:", error);
    return respErr("Failed to get verification status");
  }
}
