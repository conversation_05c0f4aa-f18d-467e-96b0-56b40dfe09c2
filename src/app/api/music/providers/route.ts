import { NextResponse } from "next/server";
import { MusicProviderFactory } from "@/services/music-provider";

export async function GET() {
  try {
    // 获取所有可用的提供商
    const providers = MusicProviderFactory.getAllProviders();
    
    // 获取默认提供商
    const defaultProvider = MusicProviderFactory.getDefaultProvider();
    const defaultProviderName = defaultProvider?.getProviderInfo().name || null;
    
    // 根据环境变量配置过滤可用的提供商
    const availableProviders = providers.filter(provider => {
      switch (provider.name) {
        case "volcano":
          return !!(process.env.VOLCANO_ACCESS_KEY_ID && process.env.VOLCANO_SECRET_ACCESS_KEY);
        case "mubert":
          return !!process.env.MUBERT_API_KEY;
        case "suno":
          return !!process.env.SUNO_API_KEY;
        default:
          return false;
      }
    });

    // 如果没有配置任何提供商，返回默认配置
    if (availableProviders.length === 0) {
      return NextResponse.json({
        providers: [
          {
            name: "volcano",
            display_name: "Volcengine",
            description: "Professional AI music generation",
            features: ["High Quality", "Fast Generation", "Multiple Styles"],
            available: false,
            reason: "API credentials not configured"
          }
        ],
        default: null,
        total: 0
      });
    }

    return NextResponse.json({
      providers: availableProviders.map(provider => ({
        ...provider,
        available: true
      })),
      default: defaultProviderName,
      total: availableProviders.length
    });
  } catch (error) {
    console.error("Failed to get music providers:", error);
    return NextResponse.json(
      { error: "Failed to get music providers" },
      { status: 500 }
    );
  }
}
