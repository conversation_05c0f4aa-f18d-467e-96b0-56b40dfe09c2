import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findTrackByUuid } from "@/models/track";
import { getTrackStems, insertTrackStems, trackHasStems } from "@/models/track-stem";
import { getUuid } from "@/lib/hash";

interface GenerateStemsRequest {
  track_uuid: string;
  stem_types?: string[];
  quality?: "standard" | "high";
  force_regenerate?: boolean;
}

interface GenerateStemsResponse {
  code: number;
  message: string;
  data: {
    track_uuid: string;
    generation_id: string;
    estimated_time: number;
    status: "pending" | "processing" | "completed" | "failed";
    stem_types: string[];
  };
}

// Available stem types
const AVAILABLE_STEM_TYPES = [
  "drums",
  "bass", 
  "melody",
  "harmony",
  "vocals",
  "percussion",
  "lead",
  "pad"
];

export async function POST(req: Request) {
  try {
    const body: GenerateStemsRequest = await req.json();
    const { 
      track_uuid, 
      stem_types = AVAILABLE_STEM_TYPES, 
      quality = "standard",
      force_regenerate = false 
    } = body;

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Validate stem types
    const invalidTypes = stem_types.filter(type => !AVAILABLE_STEM_TYPES.includes(type));
    if (invalidTypes.length > 0) {
      return respErr(`Invalid stem types: ${invalidTypes.join(", ")}`);
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check if user owns the track or if it's public
    if (track.user_uuid !== user_uuid && !track.is_public) {
      return respErr("Access denied");
    }

    // Check if stems already exist (unless force regenerate)
    if (!force_regenerate) {
      const hasStems = await trackHasStems(track_uuid);
      if (hasStems) {
        const existingStems = await getTrackStems(track_uuid);
        return respData({
          track_uuid,
          generation_id: null,
          estimated_time: 0,
          status: "completed" as const,
          stem_types: existingStems.map(stem => stem.stem_type),
          message: "Stems already exist. Use force_regenerate=true to regenerate.",
        });
      }
    }

    // Generate unique generation ID
    const generation_id = getUuid();

    // Start stem generation process
    console.log("Starting stem generation:", {
      track_uuid,
      generation_id,
      stem_types,
      quality,
      user_uuid,
    });

    // In a real implementation, this would queue a background job
    // For now, we'll simulate the process
    try {
      const { StemGenerationService } = await import("@/services/stem-generation");
      
      // Start the generation process asynchronously
      StemGenerationService.generateStems(track_uuid, stem_types, {
        quality,
        generation_id,
        user_uuid,
      }).catch(error => {
        console.error("Stem generation failed:", error);
      });

      // Estimate completion time based on track duration and number of stems
      const baseTime = track.duration * 2; // 2 seconds per second of audio
      const stemMultiplier = stem_types.length * 0.5; // Additional time per stem
      const qualityMultiplier = quality === "high" ? 1.5 : 1;
      const estimated_time = Math.ceil(baseTime * stemMultiplier * qualityMultiplier);

      const response: GenerateStemsResponse = {
        code: 0,
        message: "Stem generation started",
        data: {
          track_uuid,
          generation_id,
          estimated_time,
          status: "pending",
          stem_types,
        },
      };

      return respData(response.data);
    } catch (error) {
      console.error("Failed to start stem generation:", error);
      return respErr("Failed to start stem generation");
    }
  } catch (error) {
    console.error("Stem generation API error:", error);
    return respErr("Failed to process stem generation request");
  }
}

// GET method to check generation status
export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const track_uuid = url.searchParams.get("track_uuid");
    const generation_id = url.searchParams.get("generation_id");

    if (!track_uuid) {
      return respErr("Missing track_uuid parameter");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    // Find the track
    const track = await findTrackByUuid(track_uuid);
    if (!track) {
      return respErr("Track not found");
    }

    // Check access permissions
    if (track.user_uuid !== user_uuid && !track.is_public) {
      return respErr("Access denied");
    }

    // Get existing stems
    const stems = await getTrackStems(track_uuid);
    
    if (stems.length === 0) {
      return respData({
        track_uuid,
        generation_id,
        status: "pending",
        stems: [],
        message: "No stems found. Generation may still be in progress.",
      });
    }

    return respData({
      track_uuid,
      generation_id,
      status: "completed",
      stems: stems.map(stem => ({
        uuid: stem.uuid,
        stem_type: stem.stem_type,
        file_url: stem.file_url,
        file_size: stem.file_size,
        created_at: stem.created_at,
      })),
      message: "Stems generation completed",
    });
  } catch (error) {
    console.error("Stem status API error:", error);
    return respErr("Failed to get stem generation status");
  }
}
