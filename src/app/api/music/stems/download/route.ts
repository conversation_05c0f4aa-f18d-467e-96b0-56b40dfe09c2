import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";
import { findTrackByUuid, incrementTrackDownloadCount } from "@/models/track";
import { getTrackStems, findTrackStemByUuid } from "@/models/track-stem";
import { NextRequest } from "next/server";

interface DownloadStemsRequest {
  track_uuid?: string;
  stem_uuid?: string;
  stem_types?: string[];
  format?: "zip" | "individual";
}

export async function POST(req: Request) {
  try {
    const body: DownloadStemsRequest = await req.json();
    const { 
      track_uuid, 
      stem_uuid, 
      stem_types, 
      format = "individual" 
    } = body;

    if (!track_uuid && !stem_uuid) {
      return respErr("Either track_uuid or stem_uuid is required");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    let track;
    let stems;

    if (stem_uuid) {
      // Download specific stem
      const stem = await findTrackStemByUuid(stem_uuid);
      if (!stem) {
        return respErr("Stem not found");
      }

      track = await findTrackByUuid(stem.track_uuid);
      if (!track) {
        return respErr("Track not found");
      }

      stems = [stem];
    } else if (track_uuid) {
      // Download stems for track
      track = await findTrackByUuid(track_uuid);
      if (!track) {
        return respErr("Track not found");
      }

      stems = await getTrackStems(track_uuid);
      
      // Filter by stem types if specified
      if (stem_types && stem_types.length > 0) {
        stems = stems.filter(stem => stem_types.includes(stem.stem_type));
      }
    }

    if (!track || !stems || stems.length === 0) {
      return respErr("No stems found");
    }

    // Check access permissions
    if (track.user_uuid !== user_uuid && !track.is_public) {
      return respErr("Access denied");
    }

    // TODO: Check user subscription for stem download permissions
    // For now, assume all authenticated users can download stems

    // Increment download count
    await incrementTrackDownloadCount(track.uuid);

    if (format === "zip" && stems.length > 1) {
      // Return zip download URL
      const zipUrl = await generateZipDownloadUrl(stems, track.uuid);
      
      return respData({
        download_type: "zip",
        download_url: zipUrl,
        stems: stems.map(stem => ({
          uuid: stem.uuid,
          stem_type: stem.stem_type,
          file_size: stem.file_size,
        })),
        total_size: stems.reduce((sum, stem) => sum + (stem.file_size || 0), 0),
        expires_at: new Date(Date.now() + 3600000).toISOString(), // 1 hour
      });
    } else {
      // Return individual download URLs
      return respData({
        download_type: "individual",
        stems: stems.map(stem => ({
          uuid: stem.uuid,
          stem_type: stem.stem_type,
          download_url: stem.file_url,
          file_size: stem.file_size,
          file_format: stem.file_format,
        })),
        expires_at: new Date(Date.now() + 3600000).toISOString(), // 1 hour
      });
    }
  } catch (error) {
    console.error("Stem download API error:", error);
    return respErr("Failed to process stem download request");
  }
}

// GET method for direct stem download
export async function GET(req: NextRequest) {
  try {
    const url = new URL(req.url);
    const stem_uuid = url.searchParams.get("stem_uuid");
    const track_uuid = url.searchParams.get("track_uuid");

    if (!stem_uuid && !track_uuid) {
      return respErr("Either stem_uuid or track_uuid is required");
    }

    // Get user authentication
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("Authentication required");
    }

    let stem;
    let track;

    if (stem_uuid) {
      stem = await findTrackStemByUuid(stem_uuid);
      if (!stem) {
        return respErr("Stem not found");
      }

      track = await findTrackByUuid(stem.track_uuid);
    } else if (track_uuid) {
      track = await findTrackByUuid(track_uuid);
      if (!track) {
        return respErr("Track not found");
      }

      const stems = await getTrackStems(track_uuid);
      if (stems.length === 0) {
        return respErr("No stems found for this track");
      }

      // Return info about all stems
      return respData({
        track_uuid,
        stems: stems.map(s => ({
          uuid: s.uuid,
          stem_type: s.stem_type,
          file_url: s.file_url,
          file_size: s.file_size,
          file_format: s.file_format,
          created_at: s.created_at,
        })),
      });
    }

    if (!track) {
      return respErr("Track not found");
    }

    // Check access permissions
    if (track.user_uuid !== user_uuid && !track.is_public) {
      return respErr("Access denied");
    }

    if (stem) {
      // Redirect to actual file URL
      return Response.redirect(stem.file_url, 302);
    }

    return respErr("Invalid request");
  } catch (error) {
    console.error("Stem download GET error:", error);
    return respErr("Failed to process stem download");
  }
}

/**
 * Generate a temporary ZIP download URL for multiple stems
 */
async function generateZipDownloadUrl(
  stems: any[],
  track_uuid: string
): Promise<string> {
  // In a real implementation, this would:
  // 1. Create a temporary ZIP file containing all stems
  // 2. Upload it to storage with a temporary URL
  // 3. Return the temporary URL
  
  // For now, return a mock URL
  const zipId = `${track_uuid}-${Date.now()}`;
  return `https://storage.example.com/temp/stems-${zipId}.zip`;
}

/**
 * Create a ZIP file from multiple stem files
 */
async function createStemZip(
  stems: any[],
  outputPath: string
): Promise<string> {
  // In a real implementation, this would use a ZIP library
  // to create a ZIP file containing all the stem files
  
  console.log("Creating ZIP file:", { stems, outputPath });
  
  // Mock implementation
  return outputPath;
}

/**
 * Generate signed URL for secure downloads
 */
async function generateSignedUrl(
  fileUrl: string,
  expiresIn: number = 3600
): Promise<string> {
  // In a real implementation, this would generate a signed URL
  // for secure, time-limited access to the file
  
  // For now, return the original URL
  return fileUrl;
}
