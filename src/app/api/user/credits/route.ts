import { NextRequest, NextResponse } from "next/server";
import { getUserCredits } from "@/services/credit";
import { getUserUuid } from "@/services/user";

export async function GET(request: NextRequest) {
  try {
    const user_uuid = await getUserUuid();

    if (!user_uuid) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const credits = await getUserCredits(user_uuid);

    return NextResponse.json({
      credits: credits?.left_credits || 0,
      success: true
    });
  } catch (error) {
    console.error("Failed to get user credits:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
