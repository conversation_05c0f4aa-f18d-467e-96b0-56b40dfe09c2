@import "tailwindcss";
@import "./theme.css";

@plugin "tailwindcss-animate";

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-background text-foreground relative;
    background-attachment: fixed;
  }

  /* Tech-inspired background */
  body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
  }

  .dark body::before {
    background:
      radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.15) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.08) 0%, transparent 50%);
  }
}

.container {
  @apply mx-auto max-w-7xl px-4 md:px-8;
}

input,
select,
textarea {
  @apply border-border outline-ring/50 bg-background transition-all duration-200;
}

input:focus,
select:focus,
textarea:focus {
  @apply ring-2 ring-primary/20 border-primary/50;
}

button {
  @apply cursor-pointer border-border outline-ring/50 transition-all duration-200;
}

button:hover {
  @apply scale-[1.02];
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--muted);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neon-purple);
}

/* Selection styling */
::selection {
  background: var(--primary);
  color: var(--primary-foreground);
}
