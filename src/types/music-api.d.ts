// Music API Request/Response Types

// Generate Music API
export interface GenerateMusicRequest {
  prompt: string;
  style?: string;
  mood?: string;
  bpm?: number;
  duration: 15 | 30 | 60;
  provider?: string;
  options?: MusicGenerationOptions;
}

export interface GenerateMusicResponse {
  code: number;
  message: string;
  data: {
    generation_uuid: string;
    estimated_completion_time: number;
    status: string;
  };
}

// Music Status API
export interface MusicStatusRequest {
  generation_uuid: string;
}

export interface MusicStatusResponse {
  code: number;
  message: string;
  data: {
    generation_uuid: string;
    status: MusicGenerationStatus;
    progress?: number; // 0-100
    track?: Track;
    error_message?: string;
  };
}

// Download Music API
export interface DownloadMusicRequest {
  track_uuid: string;
  format?: "mp3" | "wav";
  quality?: "standard" | "high";
}

export interface DownloadMusicResponse {
  code: number;
  message: string;
  data: {
    download_url: string;
    expires_at: string;
    file_size: number;
    format: string;
  };
}

// Verify Loop API
export interface VerifyLoopRequest {
  track_uuid: string;
  method?: "ml" | "signal_processing";
}

export interface VerifyLoopResponse {
  code: number;
  message: string;
  data: {
    track_uuid: string;
    is_seamless: boolean;
    verification_score: number;
    analysis_details?: {
      start_analysis: AudioAnalysis;
      end_analysis: AudioAnalysis;
      similarity_score: number;
    };
  };
}

// Generate Variation API
export interface GenerateVariationRequest {
  track_uuid: string;
  variation_type: VariationType;
  variation_params: VariationParams;
}

export interface GenerateVariationResponse {
  code: number;
  message: string;
  data: {
    variation_uuid: string;
    original_track_uuid: string;
    status: string;
    estimated_completion_time: number;
  };
}

// Generate Stems API
export interface GenerateStemsRequest {
  track_uuid: string;
  stem_types?: StemType[];
}

export interface GenerateStemsResponse {
  code: number;
  message: string;
  data: {
    track_uuid: string;
    stems: TrackStem[];
    processing_status: string;
  };
}

// Track List API
export interface GetTracksRequest {
  user_uuid?: string;
  search?: string;
  style?: string;
  mood?: string;
  bpm_min?: number;
  bpm_max?: number;
  duration?: number;
  is_public?: boolean;
  sort_by?: "created_at" | "title" | "bpm" | "download_count";
  sort_order?: "asc" | "desc";
  page?: number;
  per_page?: number;
}

export interface GetTracksResponse {
  code: number;
  message: string;
  data: {
    tracks: Track[];
    pagination: {
      total: number;
      page: number;
      per_page: number;
      total_pages: number;
      has_more: boolean;
    };
    filters?: {
      available_styles: string[];
      available_moods: string[];
      bpm_range: { min: number; max: number };
    };
  };
}

// Track Detail API
export interface GetTrackRequest {
  track_uuid?: string;
  slug?: string;
}

export interface GetTrackResponse {
  code: number;
  message: string;
  data: {
    track: Track;
    variations?: TrackVariation[];
    stems?: TrackStem[];
    loop_verification?: LoopVerification;
    related_tracks?: Track[];
  };
}

// Collection APIs
export interface CreateCollectionRequest {
  name: string;
  description?: string;
  is_public?: boolean;
}

export interface CreateCollectionResponse {
  code: number;
  message: string;
  data: {
    collection: UserTrackCollection;
  };
}

export interface AddToCollectionRequest {
  collection_uuid: string;
  track_uuid: string;
}

export interface AddToCollectionResponse {
  code: number;
  message: string;
  data: {
    collection_uuid: string;
    track_uuid: string;
    added_at: string;
  };
}

export interface GetCollectionsResponse {
  code: number;
  message: string;
  data: {
    collections: UserTrackCollection[];
    total: number;
  };
}

// Music Provider APIs
export interface GetProvidersResponse {
  code: number;
  message: string;
  data: {
    providers: MusicProvider[];
    default_provider: string;
  };
}

// Analytics APIs
export interface TrackAnalyticsResponse {
  code: number;
  message: string;
  data: {
    track_uuid: string;
    total_downloads: number;
    daily_downloads: { date: string; count: number }[];
    popular_formats: { format: string; count: number }[];
    geographic_distribution?: { country: string; count: number }[];
  };
}

export interface UserMusicStatsResponse {
  code: number;
  message: string;
  data: {
    total_tracks: number;
    total_downloads: number;
    total_generations: number;
    favorite_styles: { style: string; count: number }[];
    monthly_activity: { month: string; generations: number; downloads: number }[];
  };
}

// Error Types
export interface MusicAPIError {
  code: number;
  message: string;
  details?: {
    field?: string;
    reason?: string;
    suggestion?: string;
  };
}

// Webhook Types
export interface MusicGenerationWebhook {
  event_type: "generation.completed" | "generation.failed";
  generation_uuid: string;
  user_uuid: string;
  timestamp: string;
  data: {
    track?: Track;
    error_message?: string;
  };
}

export interface LoopVerificationWebhook {
  event_type: "verification.completed";
  track_uuid: string;
  user_uuid: string;
  timestamp: string;
  data: {
    is_seamless: boolean;
    verification_score: number;
  };
}
