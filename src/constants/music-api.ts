// Music API Constants based on Volcano API documentation
// These constants match the exact values supported by the Volcano Music Generation API

export const MUSIC_GENRES = [
  "corporate",
  "dance/edm", 
  "orchestral",
  "chill out",
  "rock",
  "hip hop",
  "folk",
  "funk",
  "ambient",
  "holiday",
  "jazz",
  "kids",
  "world",
  "travel",
  "commercial",
  "advertising",
  "driving",
  "cinematic",
  "upbeat",
  "epic",
  "inspiring",
  "business",
  "video game",
  "dark",
  "pop",
  "trailer",
  "modern",
  "electronic",
  "documentary",
  "soundtrack",
  "fashion",
  "acoustic",
  "movie",
  "tv",
  "high tech",
  "industrial",
  "dance",
  "video",
  "vlog",
  "marketing",
  "game",
  "radio",
  "promotional",
  "sports",
  "party",
  "summer",
  "beauty"
] as const;

export const MUSIC_MOODS = [
  "positive",
  "uplifting",
  "energetic",
  "happy",
  "bright",
  "optimistic",
  "hopeful",
  "cool",
  "dreamy",
  "fun",
  "light",
  "powerful",
  "calm",
  "confident",
  "joyful",
  "dramatic",
  "peaceful",
  "playful",
  "soft",
  "groovy",
  "reflective",
  "easy",
  "relaxed",
  "lively",
  "smooth",
  "romantic",
  "intense",
  "elegant",
  "mellow",
  "emotional",
  "sentimental",
  "cheerful happy",
  "contemplative",
  "soothing",
  "proud",
  "passionate",
  "sweet",
  "mystical",
  "tranquil",
  "cheerful",
  "casual",
  "beautiful",
  "ethereal",
  "melancholy",
  "sad",
  "aggressive",
  "haunting",
  "adventure",
  "serene",
  "sincere",
  "funky",
  "funny"
] as const;

export const MUSIC_INSTRUMENTS = [
  "piano",
  "drums",
  "guitar",
  "percussion",
  "synth",
  "electric guitar",
  "acoustic guitar",
  "bass guitar",
  "brass",
  "violin",
  "cello",
  "flute",
  "organ",
  "trumpet",
  "ukulele",
  "saxophone",
  "double bass",
  "harp",
  "glockenspiel",
  "synthesizer",
  "keyboard",
  "marimba",
  "bass",
  "banjo",
  "strings"
] as const;

export const MUSIC_THEMES = [
  "inspirational",
  "motivational",
  "achievement",
  "discovery",
  "every day",
  "love",
  "technology",
  "lifestyle",
  "journey",
  "meditation",
  "drama",
  "children",
  "hope",
  "fantasy",
  "holiday",
  "health",
  "family",
  "real estate",
  "media",
  "kids",
  "science",
  "education",
  "progress",
  "world",
  "vacation",
  "training",
  "christmas",
  "sales"
] as const;

// Chinese translations for UI display
export const GENRE_TRANSLATIONS: Record<string, string> = {
  "corporate": "企业",
  "dance/edm": "舞蹈/电子舞曲",
  "orchestral": "管弦乐",
  "chill out": "轻松",
  "rock": "摇滚",
  "hip hop": "嘻哈",
  "folk": "民谣",
  "funk": "放克",
  "ambient": "氛围音乐",
  "holiday": "假期",
  "jazz": "爵士乐",
  "kids": "童乐",
  "world": "世界",
  "travel": "旅行",
  "commercial": "商业的",
  "advertising": "广告营销",
  "driving": "驾驶",
  "cinematic": "电影",
  "upbeat": "乐观的",
  "epic": "史诗",
  "inspiring": "鼓舞人心的",
  "business": "商业",
  "video game": "视频游戏",
  "dark": "黑暗的",
  "pop": "流行音乐",
  "trailer": "预告片",
  "modern": "现代的",
  "electronic": "电子的",
  "documentary": "记录",
  "soundtrack": "原声带",
  "fashion": "时尚",
  "acoustic": "自然声",
  "movie": "电影",
  "tv": "电视",
  "high tech": "高科技",
  "industrial": "工业的",
  "dance": "舞蹈",
  "video": "视频",
  "vlog": "视频博客",
  "marketing": "营销",
  "game": "游戏",
  "radio": "收音机",
  "promotional": "促销",
  "sports": "运动的",
  "party": "派对",
  "summer": "夏天",
  "beauty": "美丽"
};

export const MOOD_TRANSLATIONS: Record<string, string> = {
  "positive": "积极的",
  "uplifting": "令人振奋的",
  "energetic": "精力充沛",
  "happy": "快乐",
  "bright": "明亮的",
  "optimistic": "乐观的",
  "hopeful": "充满希望的",
  "cool": "酷",
  "dreamy": "梦幻般的",
  "fun": "乐趣",
  "light": "轻音乐",
  "powerful": "强力的",
  "calm": "冷静的",
  "confident": "自信的",
  "joyful": "喜悦",
  "dramatic": "戏剧性",
  "peaceful": "平静的",
  "playful": "俏皮的",
  "soft": "轻柔",
  "groovy": "时髦的",
  "reflective": "反省性的",
  "easy": "简单的",
  "relaxed": "轻松",
  "lively": "活泼",
  "smooth": "平滑的",
  "romantic": "浪漫的",
  "intense": "激烈的",
  "elegant": "优雅的",
  "mellow": "醇厚",
  "emotional": "情绪化的",
  "sentimental": "多愁善感的",
  "cheerful happy": "开朗快乐",
  "contemplative": "沉思的",
  "soothing": "舒缓的",
  "proud": "自豪的",
  "passionate": "热情的",
  "sweet": "甜蜜",
  "mystical": "神秘",
  "tranquil": "宁静",
  "cheerful": "令人愉快的",
  "casual": "随意的",
  "beautiful": "美丽的",
  "ethereal": "空灵的",
  "melancholy": "忧郁的",
  "sad": "伤心",
  "aggressive": "挑衅的",
  "haunting": "令人难以忘怀的",
  "adventure": "冒险",
  "serene": "安详",
  "sincere": "真诚",
  "funky": "节奏强适宜跳舞的",
  "funny": "搞笑"
};

export const THEME_TRANSLATIONS: Record<string, string> = {
  "inspirational": "灵感",
  "motivational": "激励",
  "achievement": "成就",
  "discovery": "发现",
  "every day": "日常",
  "love": "爱情",
  "technology": "科技",
  "lifestyle": "生活方式",
  "journey": "旅行",
  "meditation": "冥想",
  "drama": "戏剧",
  "children": "儿童",
  "hope": "希望",
  "fantasy": "幻想",
  "holiday": "假日",
  "health": "健康",
  "family": "家庭",
  "real estate": "房地产",
  "media": "媒体",
  "kids": "儿童",
  "science": "科学",
  "education": "教育",
  "progress": "进步",
  "world": "世界",
  "vacation": "度假",
  "training": "培训",
  "christmas": "圣诞",
  "sales": "销售"
};

// Type definitions
export type MusicGenre = typeof MUSIC_GENRES[number];
export type MusicMood = typeof MUSIC_MOODS[number];
export type MusicInstrument = typeof MUSIC_INSTRUMENTS[number];
export type MusicTheme = typeof MUSIC_THEMES[number];

// API parameter limits
export const MAX_SELECTIONS = 10; // Maximum number of selections per category
export const DURATION_OPTIONS = [15, 30, 60] as const;
export const BPM_RANGE = { min: 60, max: 200 } as const;
