import {
  MusicGenerationRequest,
  MusicGenerationResult,
  MusicGenerationOptions,
  MusicModelConfig,
  MusicGenerationError,
  StemSeparationRequest,
  StemSeparationResult,
  VariationRequest,
  VariationResult,
} from "../types";
import { BaseMusicProvider } from "./base-provider";

export class SunoProvider extends BaseMusicProvider {
  protected readonly providerName = "suno";
  protected readonly supportedDurations = [30, 60];
  protected readonly supportedStyles = [
    "pop", "rock", "electronic", "hip-hop", "jazz", "classical",
    "country", "folk", "reggae", "blues", "metal", "punk",
    "r&b", "soul", "funk", "disco", "house", "techno",
    "ambient", "experimental", "world", "latin", "indie"
  ];
  protected readonly maxBpm = 180;
  protected readonly minBpm = 70;

  constructor(config: MusicModelConfig) {
    super(config);
    this.config.base_url = config.base_url || "https://api.suno.ai/v1";
  }

  async generateMusic(
    request: MusicGenerationRequest,
    options?: MusicGenerationOptions
  ): Promise<MusicGenerationResult> {
    try {
      this.validateRequest(request);
      const task_id = this.generateTaskId();
      
      this.log("info", "Starting Suno generation", { request, options });

      const requestData = this.prepareRequest(request, options);
      
      // Suno API request format
      const sunoRequest = {
        prompt: this.buildSunoPrompt(requestData),
        duration: requestData.duration,
        style: requestData.style,
        mood: requestData.mood,
        bpm: requestData.bpm,
        seed: requestData.seed,
        temperature: requestData.temperature,
        guidance_scale: requestData.guidance_scale,
        quality: requestData.quality,
        format: requestData.format,
        instrumental: true, // For loop generation, we want instrumental
        make_instrumental: true,
      };

      const response = await this.makeRequest("/generate", sunoRequest);

      if (response.success) {
        return {
          task_id: response.id || task_id,
          status: "pending",
          estimated_completion_time: this.estimateCompletionTime(request.duration),
        };
      } else {
        throw new Error(response.error || "Suno generation failed");
      }
    } catch (error) {
      this.log("error", "Suno generation failed", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new MusicGenerationError(
        `Suno generation failed: ${errorMessage}`,
        "GENERATION_FAILED",
        "suno"
      );
    }
  }

  async checkGenerationStatus(task_id: string): Promise<MusicGenerationResult> {
    try {
      this.log("info", "Checking Suno status", { task_id });

      const response = await this.makeRequest(`/generate/${task_id}`, {}, { timeout: 10000 });

      if (response.success) {
        return {
          task_id,
          status: this.mapSunoStatus(response.status),
          progress: response.progress || this.calculateSunoProgress(response),
          file_url: response.audio_url,
          file_size: response.file_size,
          metadata: response.metadata ? this.processSunoMetadata(response.metadata) : undefined,
          error: response.error,
        };
      } else {
        return {
          task_id,
          status: "failed",
          error: response.error || "Status check failed",
        };
      }
    } catch (error) {
      this.log("error", "Suno status check failed", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        task_id,
        status: "failed",
        error: errorMessage,
      };
    }
  }

  async cancelGeneration(task_id: string): Promise<boolean> {
    try {
      this.log("info", "Cancelling Suno generation", { task_id });

      const response = await this.makeRequest(`/generate/${task_id}/cancel`, {});
      return response.success;
    } catch (error) {
      this.log("error", "Suno cancellation failed", error);
      return false;
    }
  }

  // Suno supports stem separation
  async separateStems(request: StemSeparationRequest): Promise<StemSeparationResult> {
    try {
      this.log("info", "Starting Suno stem separation", request);

      const stemRequest = {
        audio_url: request.track_url,
        stem_types: request.stem_types,
        quality: request.quality || "standard",
      };

      const response = await this.makeRequest("/separate-stems", stemRequest);

      if (response.success) {
        return {
          task_id: response.id,
          status: "pending",
        };
      } else {
        throw new Error(response.error || "Stem separation failed");
      }
    } catch (error) {
      this.log("error", "Suno stem separation failed", error);
      throw error;
    }
  }

  // Suno supports variations
  async generateVariation(request: VariationRequest): Promise<VariationResult> {
    try {
      this.log("info", "Starting Suno variation", request);

      const variationRequest = {
        original_audio_url: request.original_track_url,
        variation_type: request.variation_type,
        variation_params: request.variation_params,
      };

      const response = await this.makeRequest("/generate-variation", variationRequest);

      if (response.success) {
        return {
          task_id: response.id,
          status: "pending",
        };
      } else {
        throw new Error(response.error || "Variation generation failed");
      }
    } catch (error) {
      this.log("error", "Suno variation failed", error);
      throw error;
    }
  }

  private buildSunoPrompt(requestData: any): string {
    let prompt = requestData.prompt;
    
    // Add style and mood to prompt for better results
    if (requestData.style) {
      prompt += ` in ${requestData.style} style`;
    }
    
    if (requestData.mood) {
      prompt += ` with ${requestData.mood} mood`;
    }
    
    if (requestData.bpm) {
      prompt += ` at ${requestData.bpm} BPM`;
    }

    // Add loop-specific instructions
    prompt += ". Create a seamless loop that can repeat continuously without gaps or jarring transitions.";
    
    return prompt;
  }

  private mapSunoStatus(status: string): "pending" | "processing" | "completed" | "failed" {
    const statusMap: Record<string, "pending" | "processing" | "completed" | "failed"> = {
      "queued": "pending",
      "submitted": "pending",
      "running": "processing",
      "processing": "processing",
      "completed": "completed",
      "success": "completed",
      "failed": "failed",
      "error": "failed",
      "cancelled": "failed",
      "timeout": "failed",
    };

    return statusMap[status.toLowerCase()] || "pending";
  }

  private calculateSunoProgress(response: any): number {
    if (response.status === "completed" || response.status === "success") {
      return 100;
    }
    
    if (response.status === "processing" || response.status === "running") {
      // Suno provides progress updates
      if (response.progress !== undefined) {
        return Math.min(95, response.progress);
      }
      
      // Estimate based on time
      const elapsed = Date.now() - new Date(response.created_at).getTime();
      const estimated = response.estimated_time * 1000;
      return Math.min(90, Math.floor((elapsed / estimated) * 100));
    }
    
    return 0;
  }

  private processSunoMetadata(metadata: any) {
    return {
      duration: metadata.duration || 0,
      format: metadata.format || "mp3",
      sample_rate: metadata.sample_rate || 44100,
      bit_rate: metadata.bit_rate || 192000,
      channels: metadata.channels || 2,
      bpm: metadata.bpm,
      key: metadata.key,
      genre: metadata.genre,
      energy_level: metadata.energy,
      danceability: metadata.danceability,
      valence: metadata.valence,
    };
  }

  protected estimateCompletionTime(duration: number): number {
    // Suno takes longer but produces higher quality
    return duration * 3;
  }
}

// Factory function for creating Suno provider
export function createSunoProvider(config?: Partial<MusicModelConfig>): SunoProvider {
  const fullConfig: MusicModelConfig = {
    provider: "suno",
    model: "v3",
    api_key: config?.api_key || process.env.SUNO_API_KEY || "",
    base_url: config?.base_url || process.env.SUNO_BASE_URL,
    timeout: config?.timeout || 120000, // Suno takes longer
    max_retries: config?.max_retries || 2,
  };

  if (!fullConfig.api_key) {
    throw new Error("Suno API key is required");
  }

  return new SunoProvider(fullConfig);
}
