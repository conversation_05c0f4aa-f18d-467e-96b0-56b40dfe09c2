import {
  MusicGenerationRequest,
  MusicGenerationResult,
  MusicGenerationOptions,
  MusicModelConfig,
  MusicGenerationError,
} from "../types";
import { BaseMusicProvider } from "./base-provider";

export class <PERSON><PERSON><PERSON><PERSON>ider extends BaseMusicProvider {
  protected readonly providerName = "mubert";
  protected readonly supportedDurations = [15, 30, 60];
  protected readonly supportedStyles = [
    "ambient", "electronic", "chill", "upbeat", "corporate",
    "cinematic", "jazz", "classical", "rock", "pop", "hip-hop",
    "techno", "house", "trance", "dubstep", "drum-and-bass",
    "lofi", "synthwave", "minimal", "experimental"
  ];
  protected readonly maxBpm = 200;
  protected readonly minBpm = 60;

  constructor(config: MusicModelConfig) {
    super(config);
    this.config.base_url = config.base_url || "https://api-b2b.mubert.com/v2";
  }

  async generateMusic(
    request: MusicGenerationRequest,
    options?: MusicGenerationOptions
  ): Promise<MusicGenerationResult> {
    try {
      this.validateRequest(request);
      const task_id = this.generateTaskId();
      
      this.log("info", "Starting Mubert generation", { request, options });

      const requestData = this.prepareRequest(request, options);
      
      // Mubert API request format
      const mubertRequest = {
        method: "GenerateTrackByTags",
        params: {
          pat: this.config.api_key,
          duration: requestData.duration,
          tags: this.buildTags(requestData),
          mode: requestData.quality === "high" ? "track" : "loop",
          format: requestData.format,
        },
      };

      const response = await this.makeRequest("/generate", mubertRequest);

      if (response.status === 1) {
        // Success - Mubert returns task info
        return {
          task_id: response.data.tasks[0].id || task_id,
          status: "pending",
          estimated_completion_time: this.estimateCompletionTime(request.duration),
        };
      } else {
        throw new Error(response.error || "Mubert generation failed");
      }
    } catch (error) {
      this.log("error", "Mubert generation failed", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      throw new MusicGenerationError(
        `Mubert generation failed: ${errorMessage}`,
        "GENERATION_FAILED",
        "mubert"
      );
    }
  }

  async checkGenerationStatus(task_id: string): Promise<MusicGenerationResult> {
    try {
      this.log("info", "Checking Mubert status", { task_id });

      const statusRequest = {
        method: "CheckGenerationStatus",
        params: {
          pat: this.config.api_key,
          task_id: task_id,
        },
      };

      const response = await this.makeRequest("/status", statusRequest);

      if (response.status === 1) {
        const taskData = response.data;
        
        return {
          task_id,
          status: this.mapMubertStatus(taskData.status),
          progress: this.calculateProgress(taskData),
          file_url: taskData.download_link,
          file_size: taskData.file_size,
          metadata: taskData.metadata ? this.processMubertMetadata(taskData.metadata) : undefined,
        };
      } else {
        return {
          task_id,
          status: "failed",
          error: response.error || "Status check failed",
        };
      }
    } catch (error) {
      this.log("error", "Mubert status check failed", error);
      const errorMessage = error instanceof Error ? error.message : String(error);
      return {
        task_id,
        status: "failed",
        error: errorMessage,
      };
    }
  }

  async cancelGeneration(task_id: string): Promise<boolean> {
    try {
      this.log("info", "Cancelling Mubert generation", { task_id });

      const cancelRequest = {
        method: "CancelGeneration",
        params: {
          pat: this.config.api_key,
          task_id: task_id,
        },
      };

      const response = await this.makeRequest("/cancel", cancelRequest);
      return response.status === 1;
    } catch (error) {
      this.log("error", "Mubert cancellation failed", error);
      return false;
    }
  }

  private buildTags(requestData: any): string {
    const tags = [];
    
    if (requestData.style) {
      tags.push(requestData.style);
    }
    
    if (requestData.mood) {
      tags.push(requestData.mood);
    }
    
    if (requestData.bpm) {
      // Mubert uses BPM ranges
      if (requestData.bpm < 90) {
        tags.push("slow");
      } else if (requestData.bpm < 120) {
        tags.push("medium");
      } else {
        tags.push("fast");
      }
    }

    // Add prompt keywords
    const promptWords = requestData.prompt
      .toLowerCase()
      .split(/\s+/)
      .filter((word: string) => word.length > 3)
      .slice(0, 5); // Limit to 5 keywords
    
    tags.push(...promptWords);

    return tags.join(",");
  }

  private mapMubertStatus(status: string): "pending" | "processing" | "completed" | "failed" {
    const statusMap: Record<string, "pending" | "processing" | "completed" | "failed"> = {
      "queued": "pending",
      "processing": "processing",
      "completed": "completed",
      "ready": "completed",
      "failed": "failed",
      "error": "failed",
      "cancelled": "failed",
    };

    return statusMap[status.toLowerCase()] || "pending";
  }

  private calculateProgress(taskData: any): number {
    if (taskData.status === "completed" || taskData.status === "ready") {
      return 100;
    }
    
    if (taskData.status === "processing") {
      // Estimate progress based on elapsed time
      const elapsed = Date.now() - new Date(taskData.created_at).getTime();
      const estimated = taskData.estimated_time * 1000;
      return Math.min(90, Math.floor((elapsed / estimated) * 100));
    }
    
    return 0;
  }

  private processMubertMetadata(metadata: any) {
    return {
      duration: metadata.duration || 0,
      format: metadata.format || "mp3",
      sample_rate: metadata.sample_rate || 44100,
      bit_rate: metadata.bitrate || 128000,
      channels: metadata.channels || 2,
      bpm: metadata.bpm,
      key: metadata.key,
      genre: metadata.genre,
      energy_level: metadata.energy,
      danceability: metadata.danceability,
      valence: metadata.valence,
    };
  }

  protected estimateCompletionTime(duration: number): number {
    // Mubert is relatively fast
    return duration * 1.5;
  }
}

// Factory function for creating Mubert provider
export function createMubertProvider(config?: Partial<MusicModelConfig>): MubertProvider {
  const fullConfig: MusicModelConfig = {
    provider: "mubert",
    model: "v2",
    api_key: config?.api_key || process.env.MUBERT_API_KEY || "",
    base_url: config?.base_url || process.env.MUBERT_BASE_URL,
    timeout: config?.timeout || 60000,
    max_retries: config?.max_retries || 3,
  };

  if (!fullConfig.api_key) {
    throw new Error("Mubert API key is required");
  }

  return new MubertProvider(fullConfig);
}
