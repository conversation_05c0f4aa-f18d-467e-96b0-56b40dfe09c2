import {
  MusicGenerationRequest,
  MusicGenerationResult,
  MusicGenerationOptions,
  MusicModelConfig,
  MusicGenerationStream,
  MusicGenerationError,
  AudioAnalysisResult,
  LoopAnalysisResult,
  StemSeparationRequest,
  StemSeparationResult,
  VariationRequest,
  VariationResult,
} from "./types";

// Abstract base class for music models
export abstract class MusicModel {
  protected config: MusicModelConfig;
  protected provider: string;
  protected model: string;

  constructor(config: MusicModelConfig) {
    this.config = config;
    this.provider = config.provider;
    this.model = config.model;
  }

  // Core generation methods
  abstract generateMusic(
    request: MusicGenerationRequest,
    options?: MusicGenerationOptions
  ): Promise<MusicGenerationResult>;

  abstract checkGenerationStatus(task_id: string): Promise<MusicGenerationResult>;

  abstract cancelGeneration(task_id: string): Promise<boolean>;

  // Streaming generation (optional)
  generateMusicStream?(
    request: MusicGenerationRequest,
    options?: MusicGenerationOptions
  ): Promise<MusicGenerationStream>;

  // Audio analysis (optional)
  analyzeAudio?(audio_url: string): Promise<AudioAnalysisResult>;

  // Loop verification (optional)
  verifyLoop?(audio_url: string): Promise<LoopAnalysisResult>;

  // Stem separation (optional)
  separateStems?(request: StemSeparationRequest): Promise<StemSeparationResult>;

  // Variation generation (optional)
  generateVariation?(request: VariationRequest): Promise<VariationResult>;

  // Utility methods
  getProviderInfo() {
    return {
      provider: this.provider,
      model: this.model,
      supports_streaming: !!this.generateMusicStream,
      supports_analysis: !!this.analyzeAudio,
      supports_loop_verification: !!this.verifyLoop,
      supports_stem_separation: !!this.separateStems,
      supports_variations: !!this.generateVariation,
    };
  }

  protected async makeRequest(
    endpoint: string,
    data: any,
    options?: { timeout?: number; retries?: number }
  ): Promise<any> {
    const { timeout = this.config.timeout || 30000, retries = this.config.max_retries || 3 } = options || {};

    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);

        const response = await fetch(`${this.config.base_url}${endpoint}`, {
          method: "POST",
          headers: {
            "Authorization": `Bearer ${this.config.api_key}`,
            "Content-Type": "application/json",
            "User-Agent": "LoopCraft-AI-SDK/1.0",
          },
          body: JSON.stringify(data),
          signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new MusicGenerationError(
            errorData.message || `HTTP ${response.status}: ${response.statusText}`,
            errorData.code || "HTTP_ERROR",
            this.provider
          );
        }

        return await response.json();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === retries) {
          break;
        }

        // Exponential backoff
        const delay = Math.min(1000 * Math.pow(2, attempt), 10000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }

    throw new MusicGenerationError(
      `Failed after ${retries + 1} attempts: ${lastError?.message || 'Unknown error'}`,
      "MAX_RETRIES_EXCEEDED",
      this.provider
    );
  }

  protected validateRequest(request: MusicGenerationRequest): void {
    if (!request.prompt || request.prompt.trim().length === 0) {
      throw new MusicGenerationError("Prompt is required", "INVALID_PROMPT", this.provider);
    }

    if (request.prompt.length > 500) {
      throw new MusicGenerationError("Prompt must be less than 500 characters", "PROMPT_TOO_LONG", this.provider);
    }

    if (![15, 30, 60].includes(request.duration)) {
      throw new MusicGenerationError("Duration must be 15, 30, or 60 seconds", "INVALID_DURATION", this.provider);
    }

    if (request.bpm && (request.bpm < 60 || request.bpm > 200)) {
      throw new MusicGenerationError("BPM must be between 60 and 200", "INVALID_BPM", this.provider);
    }

    if (request.temperature && (request.temperature < 0 || request.temperature > 2)) {
      throw new MusicGenerationError("Temperature must be between 0 and 2", "INVALID_TEMPERATURE", this.provider);
    }

    if (request.guidance_scale && (request.guidance_scale < 1 || request.guidance_scale > 20)) {
      throw new MusicGenerationError("Guidance scale must be between 1 and 20", "INVALID_GUIDANCE_SCALE", this.provider);
    }
  }

  protected generateTaskId(): string {
    return `${this.provider}_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  protected estimateCompletionTime(duration: number): number {
    // Base estimation: 2x the track duration
    let baseTime = duration * 2;

    // Provider-specific adjustments can be overridden in subclasses
    return baseTime;
  }
}

// Factory function for creating music models
export function createMusicModel(config: MusicModelConfig): MusicModel {
  switch (config.provider.toLowerCase()) {
    case "mubert":
      return new MubertModel(config);
    case "suno":
      return new SunoModel(config);
    default:
      throw new MusicGenerationError(
        `Unsupported provider: ${config.provider}`,
        "UNSUPPORTED_PROVIDER"
      );
  }
}

// Placeholder implementations - these would be implemented in separate files
class MubertModel extends MusicModel {
  async generateMusic(request: MusicGenerationRequest, options?: MusicGenerationOptions): Promise<MusicGenerationResult> {
    this.validateRequest(request);
    
    const task_id = this.generateTaskId();
    
    // TODO: Implement actual Mubert API integration
    return {
      task_id,
      status: "pending",
      estimated_completion_time: this.estimateCompletionTime(request.duration),
    };
  }

  async checkGenerationStatus(task_id: string): Promise<MusicGenerationResult> {
    // TODO: Implement actual status checking
    return {
      task_id,
      status: "pending",
    };
  }

  async cancelGeneration(task_id: string): Promise<boolean> {
    // TODO: Implement cancellation
    return true;
  }
}

class SunoModel extends MusicModel {
  async generateMusic(request: MusicGenerationRequest, options?: MusicGenerationOptions): Promise<MusicGenerationResult> {
    this.validateRequest(request);
    
    const task_id = this.generateTaskId();
    
    // TODO: Implement actual Suno API integration
    return {
      task_id,
      status: "pending",
      estimated_completion_time: this.estimateCompletionTime(request.duration) * 1.5, // Suno takes longer
    };
  }

  async checkGenerationStatus(task_id: string): Promise<MusicGenerationResult> {
    // TODO: Implement actual status checking
    return {
      task_id,
      status: "pending",
    };
  }

  async cancelGeneration(task_id: string): Promise<boolean> {
    // TODO: Implement cancellation
    return true;
  }
}
