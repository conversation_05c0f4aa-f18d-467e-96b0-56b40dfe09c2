import {
  MusicGenerationRequest,
  MusicGenerationResult,
  MusicGenerationOptions,
  MusicGenerationError,
  MusicGenerationStream,
} from "./types";
import { MusicModel, createMusicModel } from "./music-model";

// Main music generation function
export async function generateMusic({
  model,
  prompt,
  duration,
  style,
  mood,
  bpm,
  options,
}: {
  model: MusicModel | string;
  prompt: string;
  duration: 15 | 30 | 60;
  style?: string;
  mood?: string;
  bpm?: number;
  options?: MusicGenerationOptions;
}): Promise<MusicGenerationResult> {
  try {
    // If model is a string, create the model instance
    let musicModel: MusicModel;
    if (typeof model === "string") {
      // Parse provider and model from string (e.g., "mubert:v1" or "suno:v2")
      const [provider, modelName = "default"] = model.split(":");
      
      if (!process.env[`${provider.toUpperCase()}_API_KEY`]) {
        throw new MusicGenerationError(
          `API key not found for provider: ${provider}`,
          "MISSING_API_KEY",
          provider
        );
      }

      musicModel = createMusicModel({
        provider,
        model: modelName,
        api_key: process.env[`${provider.toUpperCase()}_API_KEY`]!,
        base_url: process.env[`${provider.toUpperCase()}_BASE_URL`],
        timeout: 60000,
        max_retries: 3,
      });
    } else {
      musicModel = model;
    }

    // Prepare the generation request
    const request: MusicGenerationRequest = {
      prompt,
      duration,
      style,
      mood,
      bpm,
      seed: options?.seed,
      temperature: options?.temperature,
      guidance_scale: options?.guidance_scale,
    };

    // Generate the music
    const result = await musicModel.generateMusic(request, options);

    return result;
  } catch (error) {
    if (error instanceof MusicGenerationError) {
      throw error;
    }

    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new MusicGenerationError(
      `Music generation failed: ${errorMessage}`,
      "GENERATION_FAILED"
    );
  }
}

// Streaming music generation function
export async function generateMusicStream({
  model,
  prompt,
  duration,
  style,
  mood,
  bpm,
  options,
  onProgress,
  onComplete,
  onError,
}: {
  model: MusicModel | string;
  prompt: string;
  duration: 15 | 30 | 60;
  style?: string;
  mood?: string;
  bpm?: number;
  options?: MusicGenerationOptions;
  onProgress?: (progress: number) => void;
  onComplete?: (result: MusicGenerationResult) => void;
  onError?: (error: Error) => void;
}): Promise<MusicGenerationStream> {
  try {
    // Create model instance if needed
    let musicModel: MusicModel;
    if (typeof model === "string") {
      const [provider, modelName = "default"] = model.split(":");
      
      if (!process.env[`${provider.toUpperCase()}_API_KEY`]) {
        throw new MusicGenerationError(
          `API key not found for provider: ${provider}`,
          "MISSING_API_KEY",
          provider
        );
      }

      musicModel = createMusicModel({
        provider,
        model: modelName,
        api_key: process.env[`${provider.toUpperCase()}_API_KEY`]!,
        base_url: process.env[`${provider.toUpperCase()}_BASE_URL`],
        timeout: 60000,
        max_retries: 3,
      });
    } else {
      musicModel = model;
    }

    // Check if streaming is supported
    if (!musicModel.generateMusicStream) {
      // Fallback to polling-based streaming
      return createPollingStream(musicModel, {
        prompt,
        duration,
        style,
        mood,
        bpm,
        seed: options?.seed,
        temperature: options?.temperature,
        guidance_scale: options?.guidance_scale,
      }, options, onProgress, onComplete, onError);
    }

    // Use native streaming if available
    const request: MusicGenerationRequest = {
      prompt,
      duration,
      style,
      mood,
      bpm,
      seed: options?.seed,
      temperature: options?.temperature,
      guidance_scale: options?.guidance_scale,
    };

    const stream = await musicModel.generateMusicStream(request, options);

    // Attach callbacks
    if (onProgress) stream.onProgress = onProgress;
    if (onComplete) stream.onComplete = onComplete;
    if (onError) stream.onError = onError;

    return stream;
  } catch (error) {
    if (onError) {
      onError(error as Error);
    }
    
    throw error;
  }
}

// Create a polling-based stream for providers that don't support native streaming
async function createPollingStream(
  model: MusicModel,
  request: MusicGenerationRequest,
  options?: MusicGenerationOptions,
  onProgress?: (progress: number) => void,
  onComplete?: (result: MusicGenerationResult) => void,
  onError?: (error: Error) => void
): Promise<MusicGenerationStream> {
  let cancelled = false;
  let pollInterval: NodeJS.Timeout | null = null;

  try {
    // Start the generation
    const initialResult = await model.generateMusic(request, options);
    const task_id = initialResult.task_id;

    // Start polling for status updates
    const poll = async () => {
      if (cancelled) return;

      try {
        const result = await model.checkGenerationStatus(task_id);

        if (result.progress !== undefined && onProgress) {
          onProgress(result.progress);
        }

        if (result.status === "completed") {
          if (pollInterval) clearInterval(pollInterval);
          if (onComplete) onComplete(result);
        } else if (result.status === "failed") {
          if (pollInterval) clearInterval(pollInterval);
          if (onError) {
            onError(new MusicGenerationError(
              result.error || "Generation failed",
              "GENERATION_FAILED",
              undefined,
              task_id
            ));
          }
        }
      } catch (error) {
        if (pollInterval) clearInterval(pollInterval);
        if (onError) onError(error as Error);
      }
    };

    // Poll every 2 seconds
    pollInterval = setInterval(poll, 2000);

    // Initial poll
    setTimeout(poll, 1000);

    return {
      task_id,
      onProgress,
      onComplete,
      onError,
      cancel: async () => {
        cancelled = true;
        if (pollInterval) {
          clearInterval(pollInterval);
          pollInterval = null;
        }
        return await model.cancelGeneration(task_id);
      },
    };
  } catch (error) {
    if (onError) onError(error as Error);
    throw error;
  }
}

// Utility function to check generation status
export async function checkMusicGenerationStatus(
  model: MusicModel | string,
  task_id: string
): Promise<MusicGenerationResult> {
  try {
    let musicModel: MusicModel;
    if (typeof model === "string") {
      const [provider, modelName = "default"] = model.split(":");
      
      musicModel = createMusicModel({
        provider,
        model: modelName,
        api_key: process.env[`${provider.toUpperCase()}_API_KEY`]!,
        base_url: process.env[`${provider.toUpperCase()}_BASE_URL`],
        timeout: 30000,
        max_retries: 3,
      });
    } else {
      musicModel = model;
    }

    return await musicModel.checkGenerationStatus(task_id);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    throw new MusicGenerationError(
      `Failed to check generation status: ${errorMessage}`,
      "STATUS_CHECK_FAILED"
    );
  }
}

// Utility function to cancel generation
export async function cancelMusicGeneration(
  model: MusicModel | string,
  task_id: string
): Promise<boolean> {
  try {
    let musicModel: MusicModel;
    if (typeof model === "string") {
      const [provider, modelName = "default"] = model.split(":");
      
      musicModel = createMusicModel({
        provider,
        model: modelName,
        api_key: process.env[`${provider.toUpperCase()}_API_KEY`]!,
        base_url: process.env[`${provider.toUpperCase()}_BASE_URL`],
        timeout: 30000,
        max_retries: 3,
      });
    } else {
      musicModel = model;
    }

    return await musicModel.cancelGeneration(task_id);
  } catch (error) {
    console.error("Failed to cancel generation:", error);
    return false;
  }
}
