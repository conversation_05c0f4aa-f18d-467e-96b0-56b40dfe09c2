// Music generation types for AI SDK

export interface MusicGenerationRequest {
  prompt: string;
  duration: 15 | 30 | 60;
  style?: string;
  mood?: string;
  bpm?: number;
  seed?: number;
  temperature?: number;
  guidance_scale?: number;
}

export interface MusicGenerationResult {
  task_id: string;
  status: "pending" | "processing" | "completed" | "failed";
  progress?: number;
  estimated_completion_time?: number;
  file_url?: string;
  file_size?: number;
  metadata?: MusicMetadata;
  error?: string;
}

export interface MusicMetadata {
  duration: number;
  format: string;
  sample_rate?: number;
  bit_rate?: number;
  channels?: number;
  bpm?: number;
  key?: string;
  genre?: string;
  energy_level?: number;
  danceability?: number;
  valence?: number;
}

export interface MusicProvider {
  name: string;
  display_name: string;
  supported_durations: number[];
  supported_styles: string[];
  max_bpm: number;
  min_bpm: number;
  supports_stems: boolean;
  supports_variations: boolean;
  api_endpoint: string;
}

export interface MusicModelConfig {
  provider: string;
  model: string;
  api_key: string;
  base_url?: string;
  timeout?: number;
  max_retries?: number;
}

export interface MusicGenerationOptions {
  provider?: string;
  model?: string;
  quality?: "standard" | "high" | "premium";
  format?: "mp3" | "wav";
  seed?: number;
  style?: string;
  mood?: string;
  bpm?: number;
  temperature?: number;
  guidance_scale?: number;
  negative_prompt?: string;
}

// Callback types for async generation
export type MusicGenerationCallback = (result: MusicGenerationResult) => void;
export type MusicGenerationErrorCallback = (error: Error) => void;

// Streaming types for real-time generation updates
export interface MusicGenerationStream {
  task_id: string;
  onProgress?: (progress: number) => void;
  onComplete?: (result: MusicGenerationResult) => void;
  onError?: (error: Error) => void;
  cancel: () => Promise<boolean>;
}

// Audio analysis types
export interface AudioAnalysisResult {
  duration: number;
  bpm: number;
  key: string;
  energy_level: number;
  danceability: number;
  valence: number;
  spectral_features: {
    spectral_centroid: number;
    spectral_rolloff: number;
    zero_crossing_rate: number;
    mfcc: number[];
  };
  rhythm_features: {
    tempo_stability: number;
    beat_strength: number;
    rhythm_complexity: number;
  };
}

// Loop verification types
export interface LoopAnalysisResult {
  is_seamless: boolean;
  similarity_score: number;
  transition_quality: number;
  start_features: AudioFeatures;
  end_features: AudioFeatures;
  recommendations?: string[];
}

export interface AudioFeatures {
  frequency_spectrum: number[];
  amplitude: number;
  phase: number;
  spectral_centroid: number;
  zero_crossing_rate: number;
  energy: number;
}

// Stem separation types
export interface StemSeparationRequest {
  track_url: string;
  stem_types: StemType[];
  quality?: "standard" | "high";
}

export interface StemSeparationResult {
  task_id: string;
  status: "pending" | "processing" | "completed" | "failed";
  stems?: StemFile[];
  error?: string;
}

export interface StemFile {
  type: StemType;
  file_url: string;
  file_size: number;
  duration: number;
}

export type StemType = 
  | "drums" 
  | "bass" 
  | "melody" 
  | "harmony" 
  | "vocals" 
  | "percussion"
  | "lead"
  | "pad";

// Variation generation types
export interface VariationRequest {
  original_track_url: string;
  variation_type: VariationType;
  variation_params: VariationParams;
}

export interface VariationResult {
  task_id: string;
  status: "pending" | "processing" | "completed" | "failed";
  variation_url?: string;
  variation_metadata?: MusicMetadata;
  error?: string;
}

export type VariationType = "tempo" | "style" | "mood" | "key" | "arrangement";

export interface VariationParams {
  tempo_change?: number; // percentage change (-50 to +100)
  style_target?: string;
  mood_target?: string;
  key_target?: string;
  arrangement_type?: "minimal" | "full" | "orchestral" | "electronic";
  intensity?: number; // 0.0 to 1.0
}

// Error types
export class MusicGenerationError extends Error {
  constructor(
    message: string,
    public code: string,
    public provider?: string,
    public task_id?: string
  ) {
    super(message);
    this.name = "MusicGenerationError";
  }
}

export class MusicProviderError extends Error {
  constructor(
    message: string,
    public provider: string,
    public status_code?: number
  ) {
    super(message);
    this.name = "MusicProviderError";
  }
}

// Utility types
export interface MusicGenerationStats {
  total_generations: number;
  successful_generations: number;
  failed_generations: number;
  average_generation_time: number;
  popular_styles: { style: string; count: number }[];
  popular_durations: { duration: number; count: number }[];
}
