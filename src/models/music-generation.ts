import { musicGenerations } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and, gte, lte, inArray } from "drizzle-orm";
import { MusicGeneration, MusicGenerationStatus } from "@/types/music";

// Insert new music generation request
export async function insertMusicGeneration(
  data: typeof musicGenerations.$inferInsert
): Promise<typeof musicGenerations.$inferSelect | undefined> {
  const [generation] = await db()
    .insert(musicGenerations)
    .values(data)
    .returning();

  return generation;
}

// Find music generation by UUID
export async function findMusicGenerationByUuid(
  uuid: string
): Promise<typeof musicGenerations.$inferSelect | undefined> {
  const [generation] = await db()
    .select()
    .from(musicGenerations)
    .where(eq(musicGenerations.uuid, uuid))
    .limit(1);

  return generation;
}

// Find music generation by provider task ID
export async function findMusicGenerationByProviderTaskId(
  provider_task_id: string
): Promise<typeof musicGenerations.$inferSelect | undefined> {
  const [generation] = await db()
    .select()
    .from(musicGenerations)
    .where(eq(musicGenerations.provider_task_id, provider_task_id))
    .limit(1);

  return generation;
}

// Update music generation status
export async function updateMusicGenerationStatus(
  uuid: string,
  status: MusicGenerationStatus,
  error_message?: string
): Promise<typeof musicGenerations.$inferSelect | undefined> {
  const updateData: Partial<typeof musicGenerations.$inferInsert> = {
    status,
    updated_at: new Date(),
  };

  if (error_message) {
    updateData.error_message = error_message;
  }

  if (status === "completed") {
    updateData.completed_at = new Date();
  }

  const [generation] = await db()
    .update(musicGenerations)
    .set(updateData)
    .where(eq(musicGenerations.uuid, uuid))
    .returning();

  return generation;
}

// Update provider task ID
export async function updateMusicGenerationProviderTaskId(
  uuid: string,
  provider_task_id: string
): Promise<typeof musicGenerations.$inferSelect | undefined> {
  const [generation] = await db()
    .update(musicGenerations)
    .set({
      provider_task_id,
      updated_at: new Date(),
    })
    .where(eq(musicGenerations.uuid, uuid))
    .returning();

  return generation;
}

// Get user's music generations
export async function getUserMusicGenerations(
  user_uuid: string,
  limit: number = 20,
  offset: number = 0
): Promise<typeof musicGenerations.$inferSelect[]> {
  const generations = await db()
    .select()
    .from(musicGenerations)
    .where(eq(musicGenerations.user_uuid, user_uuid))
    .orderBy(desc(musicGenerations.created_at))
    .limit(limit)
    .offset(offset);

  return generations;
}

// Get music generations by status
export async function getMusicGenerationsByStatus(
  status: MusicGenerationStatus,
  limit: number = 100
): Promise<typeof musicGenerations.$inferSelect[]> {
  const generations = await db()
    .select()
    .from(musicGenerations)
    .where(eq(musicGenerations.status, status))
    .orderBy(desc(musicGenerations.created_at))
    .limit(limit);

  return generations;
}

// Get all pending/processing generations
export async function findPendingMusicGenerations(): Promise<typeof musicGenerations.$inferSelect[]> {
  const generations = await db()
    .select()
    .from(musicGenerations)
    .where(
      inArray(musicGenerations.status, ["pending", "processing"])
    )
    .orderBy(desc(musicGenerations.created_at));

  return generations;
}

// Get pending/processing generations older than specified minutes
export async function getStaleGenerations(
  minutes: number = 30
): Promise<typeof musicGenerations.$inferSelect[]> {
  const cutoffTime = new Date(Date.now() - minutes * 60 * 1000);

  const generations = await db()
    .select()
    .from(musicGenerations)
    .where(
      and(
        inArray(musicGenerations.status, ["pending", "processing"]),
        lte(musicGenerations.created_at, cutoffTime)
      )
    )
    .orderBy(desc(musicGenerations.created_at));

  return generations;
}

// Count user's generations in time period
export async function countUserGenerationsInPeriod(
  user_uuid: string,
  hours: number = 24
): Promise<number> {
  const cutoffTime = new Date(Date.now() - hours * 60 * 60 * 1000);
  
  const result = await db()
    .select({ count: musicGenerations.id })
    .from(musicGenerations)
    .where(
      and(
        eq(musicGenerations.user_uuid, user_uuid),
        gte(musicGenerations.created_at, cutoffTime)
      )
    );

  return result.length;
}

// Delete music generation
export async function deleteMusicGeneration(
  uuid: string
): Promise<boolean> {
  const result = await db()
    .delete(musicGenerations)
    .where(eq(musicGenerations.uuid, uuid));

  return result.length > 0;
}

// Get generation statistics
export async function getMusicGenerationStats(
  user_uuid?: string
): Promise<{
  total: number;
  completed: number;
  failed: number;
  pending: number;
  processing: number;
}> {
  const generations = user_uuid
    ? await db().select().from(musicGenerations).where(eq(musicGenerations.user_uuid, user_uuid))
    : await db().select().from(musicGenerations);

  const stats = {
    total: generations.length,
    completed: 0,
    failed: 0,
    pending: 0,
    processing: 0,
  };

  generations.forEach((gen) => {
    switch (gen.status) {
      case "completed":
        stats.completed++;
        break;
      case "failed":
        stats.failed++;
        break;
      case "pending":
        stats.pending++;
        break;
      case "processing":
        stats.processing++;
        break;
    }
  });

  return stats;
}

// Get user generations with pagination and filtering
export async function getUserGenerations(
  user_uuid: string,
  limit: number = 10,
  offset: number = 0,
  status?: "pending" | "processing" | "completed" | "failed"
) {
  const conditions = [eq(musicGenerations.user_uuid, user_uuid)];

  if (status) {
    conditions.push(eq(musicGenerations.status, status));
  }

  return await db()
    .select()
    .from(musicGenerations)
    .where(and(...conditions))
    .orderBy(desc(musicGenerations.created_at))
    .limit(limit)
    .offset(offset);
}
