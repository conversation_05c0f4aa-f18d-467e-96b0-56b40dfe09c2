import { trackStems } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and, inArray } from "drizzle-orm";
import { TrackStem, StemType } from "@/types/music";

// Insert new track stem
export async function insertTrackStem(
  data: typeof trackStems.$inferInsert
): Promise<typeof trackStems.$inferSelect | undefined> {
  const [stem] = await db()
    .insert(trackStems)
    .values(data)
    .returning();

  return stem;
}

// Insert multiple stems
export async function insertTrackStems(
  data: typeof trackStems.$inferInsert[]
): Promise<typeof trackStems.$inferSelect[]> {
  const stems = await db()
    .insert(trackStems)
    .values(data)
    .returning();

  return stems;
}

// Find stem by UUID
export async function findTrackStemByUuid(
  uuid: string
): Promise<typeof trackStems.$inferSelect | undefined> {
  const [stem] = await db()
    .select()
    .from(trackStems)
    .where(eq(trackStems.uuid, uuid))
    .limit(1);

  return stem;
}

// Get stems for a track
export async function getTrackStems(
  track_uuid: string
): Promise<typeof trackStems.$inferSelect[]> {
  const stems = await db()
    .select()
    .from(trackStems)
    .where(eq(trackStems.track_uuid, track_uuid))
    .orderBy(trackStems.stem_type);

  return stems;
}

// Get stems by type
export async function getStemsByType(
  track_uuid: string,
  stem_types: StemType[]
): Promise<typeof trackStems.$inferSelect[]> {
  const stems = await db()
    .select()
    .from(trackStems)
    .where(
      and(
        eq(trackStems.track_uuid, track_uuid),
        inArray(trackStems.stem_type, stem_types)
      )
    )
    .orderBy(trackStems.stem_type);

  return stems;
}

// Get specific stem type for a track
export async function getTrackStemByType(
  track_uuid: string,
  stem_type: StemType
): Promise<typeof trackStems.$inferSelect | undefined> {
  const [stem] = await db()
    .select()
    .from(trackStems)
    .where(
      and(
        eq(trackStems.track_uuid, track_uuid),
        eq(trackStems.stem_type, stem_type)
      )
    )
    .limit(1);

  return stem;
}

// Update stem
export async function updateTrackStem(
  uuid: string,
  data: Partial<typeof trackStems.$inferInsert>
): Promise<typeof trackStems.$inferSelect | undefined> {
  const [stem] = await db()
    .update(trackStems)
    .set(data)
    .where(eq(trackStems.uuid, uuid))
    .returning();

  return stem;
}

// Delete stem
export async function deleteTrackStem(uuid: string): Promise<boolean> {
  const result = await db()
    .delete(trackStems)
    .where(eq(trackStems.uuid, uuid));

  return result.length > 0;
}

// Delete all stems for a track
export async function deleteTrackStems(track_uuid: string): Promise<boolean> {
  const result = await db()
    .delete(trackStems)
    .where(eq(trackStems.track_uuid, track_uuid));

  return result.length > 0;
}

// Delete stems by type
export async function deleteTrackStemsByType(
  track_uuid: string,
  stem_types: StemType[]
): Promise<boolean> {
  const result = await db()
    .delete(trackStems)
    .where(
      and(
        eq(trackStems.track_uuid, track_uuid),
        inArray(trackStems.stem_type, stem_types)
      )
    );

  return result.length > 0;
}

// Check if track has stems
export async function trackHasStems(track_uuid: string): Promise<boolean> {
  const [stem] = await db()
    .select()
    .from(trackStems)
    .where(eq(trackStems.track_uuid, track_uuid))
    .limit(1);

  return !!stem;
}

// Check if track has specific stem types
export async function trackHasStemTypes(
  track_uuid: string,
  stem_types: StemType[]
): Promise<{ [key in StemType]?: boolean }> {
  const stems = await db()
    .select()
    .from(trackStems)
    .where(
      and(
        eq(trackStems.track_uuid, track_uuid),
        inArray(trackStems.stem_type, stem_types)
      )
    );

  const result: { [key in StemType]?: boolean } = {};
  
  stem_types.forEach(type => {
    result[type] = stems.some(stem => stem.stem_type === type);
  });

  return result;
}

// Get available stem types for a track
export async function getAvailableStemTypes(
  track_uuid: string
): Promise<StemType[]> {
  const stems = await db()
    .selectDistinct({ stem_type: trackStems.stem_type })
    .from(trackStems)
    .where(eq(trackStems.track_uuid, track_uuid));

  return stems.map(s => s.stem_type as StemType);
}

// Count stems for a track
export async function countTrackStems(track_uuid: string): Promise<number> {
  const result = await db()
    .select({ count: trackStems.id })
    .from(trackStems)
    .where(eq(trackStems.track_uuid, track_uuid));

  return result.length;
}

// Get stem statistics
export async function getStemStats(): Promise<{
  total: number;
  by_type: Record<StemType, number>;
  tracks_with_stems: number;
}> {
  const stems = await db()
    .select()
    .from(trackStems);

  const uniqueTrackUuids = new Set(stems.map(s => s.track_uuid));

  const stats = {
    total: stems.length,
    by_type: {
      drums: 0,
      bass: 0,
      melody: 0,
      harmony: 0,
      vocals: 0,
      percussion: 0,
      lead: 0,
      pad: 0,
    } as Record<StemType, number>,
    tracks_with_stems: uniqueTrackUuids.size,
  };

  stems.forEach((stem) => {
    if (stem.stem_type in stats.by_type) {
      stats.by_type[stem.stem_type as StemType]++;
    }
  });

  return stats;
}

// Get recent stems
export async function getRecentStems(
  limit: number = 20
): Promise<typeof trackStems.$inferSelect[]> {
  const recentStems = await db()
    .select()
    .from(trackStems)
    .orderBy(desc(trackStems.created_at))
    .limit(limit);

  return recentStems;
}
