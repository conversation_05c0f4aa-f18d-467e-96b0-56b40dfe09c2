import { trackVariations } from "@/db/schema";
import { db } from "@/db";
import { desc, eq, and } from "drizzle-orm";
import { TrackVariation, VariationType } from "@/types/music";

// Insert new track variation
export async function insertTrackVariation(
  data: typeof trackVariations.$inferInsert
): Promise<typeof trackVariations.$inferSelect | undefined> {
  const [variation] = await db()
    .insert(trackVariations)
    .values(data)
    .returning();

  return variation;
}

// Find variation by UUID
export async function findTrackVariationByUuid(
  uuid: string
): Promise<typeof trackVariations.$inferSelect | undefined> {
  const [variation] = await db()
    .select()
    .from(trackVariations)
    .where(eq(trackVariations.uuid, uuid))
    .limit(1);

  return variation;
}

// Get variations for a track
export async function getTrackVariations(
  original_track_uuid: string
): Promise<typeof trackVariations.$inferSelect[]> {
  const variations = await db()
    .select()
    .from(trackVariations)
    .where(eq(trackVariations.original_track_uuid, original_track_uuid))
    .orderBy(desc(trackVariations.created_at));

  return variations;
}

// Get variations by type
export async function getVariationsByType(
  original_track_uuid: string,
  variation_type: VariationType
): Promise<typeof trackVariations.$inferSelect[]> {
  const variations = await db()
    .select()
    .from(trackVariations)
    .where(
      and(
        eq(trackVariations.original_track_uuid, original_track_uuid),
        eq(trackVariations.variation_type, variation_type)
      )
    )
    .orderBy(desc(trackVariations.created_at));

  return variations;
}

// Get user's variations
export async function getUserTrackVariations(
  user_uuid: string,
  limit: number = 20,
  offset: number = 0
): Promise<typeof trackVariations.$inferSelect[]> {
  const variations = await db()
    .select()
    .from(trackVariations)
    .where(eq(trackVariations.user_uuid, user_uuid))
    .orderBy(desc(trackVariations.created_at))
    .limit(limit)
    .offset(offset);

  return variations;
}

// Update variation
export async function updateTrackVariation(
  uuid: string,
  data: Partial<typeof trackVariations.$inferInsert>
): Promise<typeof trackVariations.$inferSelect | undefined> {
  const [variation] = await db()
    .update(trackVariations)
    .set(data)
    .where(eq(trackVariations.uuid, uuid))
    .returning();

  return variation;
}

// Delete variation
export async function deleteTrackVariation(uuid: string): Promise<boolean> {
  const result = await db()
    .delete(trackVariations)
    .where(eq(trackVariations.uuid, uuid));

  return result.length > 0;
}

// Delete all variations for a track
export async function deleteTrackVariations(
  original_track_uuid: string
): Promise<boolean> {
  const result = await db()
    .delete(trackVariations)
    .where(eq(trackVariations.original_track_uuid, original_track_uuid));

  return result.length > 0;
}

// Count variations for a track
export async function countTrackVariations(
  original_track_uuid: string
): Promise<number> {
  const result = await db()
    .select({ count: trackVariations.id })
    .from(trackVariations)
    .where(eq(trackVariations.original_track_uuid, original_track_uuid));

  return result.length;
}

// Get variation statistics
export async function getVariationStats(user_uuid?: string): Promise<{
  total: number;
  by_type: Record<VariationType, number>;
}> {
  const variations = user_uuid
    ? await db().select().from(trackVariations).where(eq(trackVariations.user_uuid, user_uuid))
    : await db().select().from(trackVariations);

  const stats = {
    total: variations.length,
    by_type: {
      tempo: 0,
      style: 0,
      mood: 0,
      key: 0,
      arrangement: 0,
    } as Record<VariationType, number>,
  };

  variations.forEach((variation) => {
    if (variation.variation_type in stats.by_type) {
      stats.by_type[variation.variation_type as VariationType]++;
    }
  });

  return stats;
}
