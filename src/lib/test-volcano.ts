/**
 * Volcengine 音乐生成服务测试工具
 * 用于验证配置和API连接
 */

import { VolcanoProvider } from "@/services/volcano-provider";

export async function testVolcanoProvider() {
  try {
    // 检查环境变量
    const accessKeyId = process.env.VOLCANO_ACCESS_KEY_ID;
    const secretAccessKey = process.env.VOLCANO_SECRET_ACCESS_KEY;

    if (!accessKeyId || !secretAccessKey) {
      console.log("❌ Volcano API credentials not configured");
      console.log("Please set VOLCANO_ACCESS_KEY_ID and VOLCANO_SECRET_ACCESS_KEY in your environment");
      return false;
    }

    console.log("✅ Volcano API credentials found");

    // 创建提供商实例
    const volcanoProvider = new VolcanoProvider({
      api_key: "", // Volcano 不使用这个字段
      access_key_id: accessKeyId,
      secret_access_key: secretAccessKey,
      base_url: process.env.VOLCANO_BASE_URL,
      region: process.env.VOLCANO_REGION,
      service_name: process.env.VOLCANO_SERVICE_NAME,
      version: process.env.VOLCANO_VERSION,
      timeout: 60000,
      max_retries: 3,
    });

    console.log("✅ Volcano provider created successfully");

    // 测试音乐生成（简单测试）
    console.log("🎵 Testing music generation...");
    
    const result = await volcanoProvider.generateMusic(
      "轻松愉快的背景音乐",
      30,
      {
        provider: "volcano",
        style: "ambient",
        mood: "peaceful",
      }
    );

    console.log("✅ Music generation started:", result);

    // 测试状态查询
    console.log("📊 Testing status check...");
    
    const status = await volcanoProvider.checkStatus(result.task_id);
    console.log("✅ Status check successful:", status);

    return true;
  } catch (error) {
    console.error("❌ Volcano provider test failed:", error);
    return false;
  }
}

export async function testVolcanoProviderFactory() {
  try {
    const { MusicProviderFactory } = await import("@/services/music-provider");
    
    // 重新初始化提供商
    MusicProviderFactory.initialize();
    
    // 检查volcano提供商是否已注册
    const volcanoProvider = MusicProviderFactory.getProvider("volcano");
    if (!volcanoProvider) {
      console.log("❌ Volcano provider not registered in factory");
      return false;
    }

    console.log("✅ Volcano provider registered in factory");

    // 检查是否为默认提供商
    const defaultProvider = MusicProviderFactory.getDefaultProvider();
    if (defaultProvider && defaultProvider.constructor.name === "VolcanoMusicProvider") {
      console.log("✅ Volcano is the default provider");
    } else {
      console.log("ℹ️ Volcano is not the default provider, default is:", defaultProvider?.constructor.name || "none");
    }

    // 获取所有提供商
    const allProviders = MusicProviderFactory.getAllProviders();
    console.log("📋 Available providers:", allProviders.map(p => p.name).join(", "));

    return true;
  } catch (error) {
    console.error("❌ Provider factory test failed:", error);
    return false;
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  async function runTests() {
    console.log("🧪 Testing Volcengine Music Provider...\n");
    
    const providerTest = await testVolcanoProvider();
    console.log("");
    
    const factoryTest = await testVolcanoProviderFactory();
    console.log("");
    
    if (providerTest && factoryTest) {
      console.log("🎉 All tests passed! Volcengine provider is ready to use.");
    } else {
      console.log("💥 Some tests failed. Please check the configuration.");
    }
  }
  
  runTests().catch(console.error);
}
