/**
 * Track slug generation utilities for SEO-friendly URLs
 * Format: {prompt-keywords}-{bpm}bpm-{uuid}
 * Example: upbeat-electronic-dance-128bpm-abc123def456
 */

/**
 * Generate SEO-friendly slug from track data
 */
export function generateTrackSlug(track: {
  prompt?: string;
  title?: string;
  bpm?: number;
  uuid: string;
  style?: string;
}): string {
  // Use title first, then prompt, then style as fallback
  const text = track.title || track.prompt || track.style || "music-loop";
  
  // Extract keywords from text
  const keywords = extractKeywords(text);
  
  // Add BPM if available
  const bpmPart = track.bpm ? `${track.bpm}bpm` : "variablebpm";
  
  // Get short UUID (first 8 characters for readability)
  const shortUuid = track.uuid.substring(0, 8);
  
  // Combine parts
  const slug = `${keywords}-${bpmPart}-${shortUuid}`;
  
  return slug;
}

/**
 * Extract and clean keywords from text for URL
 */
function extractKeywords(text: string): string {
  return text
    .toLowerCase()
    // Remove common words that don't add SEO value
    .replace(/\b(a|an|the|and|or|but|in|on|at|to|for|of|with|by|from|up|about|into|through|during|before|after|above|below|between|among|around|over|under|within|without|against|across|along|beside|beyond|inside|outside|toward|towards|upon|underneath|throughout|underneath|meanwhile|however|therefore|furthermore|moreover|nevertheless|nonetheless|consequently|accordingly|subsequently|eventually|finally|initially|originally|previously|recently|currently|presently|immediately|suddenly|gradually|slowly|quickly|rapidly|frequently|occasionally|rarely|seldom|never|always|usually|often|sometimes|perhaps|maybe|possibly|probably|certainly|definitely|absolutely|completely|entirely|totally|partially|slightly|somewhat|rather|quite|very|extremely|incredibly|amazingly|surprisingly|unfortunately|fortunately|obviously|clearly|apparently|evidently|presumably|supposedly|allegedly|reportedly|seemingly|actually|really|truly|indeed|certainly|surely|undoubtedly|unquestionably|indubitably|admittedly|frankly|honestly|personally|generally|specifically|particularly|especially|notably|remarkably|significantly|importantly|interestingly|surprisingly|unfortunately|fortunately|basically|essentially|fundamentally|primarily|mainly|mostly|largely|partly|somewhat|rather|quite|fairly|pretty|relatively|comparatively|approximately|roughly|exactly|precisely|specifically|particularly|especially|notably|remarkably|significantly|importantly|interestingly)\b/g, '')
    // Replace multiple spaces with single space
    .replace(/\s+/g, ' ')
    // Trim whitespace
    .trim()
    // Replace spaces and special characters with hyphens
    .replace(/[^a-z0-9]+/g, '-')
    // Remove leading/trailing hyphens
    .replace(/^-+|-+$/g, '')
    // Limit length to 50 characters for readability
    .substring(0, 50)
    // Remove trailing hyphen if cut off
    .replace(/-+$/, '');
}

/**
 * Extract UUID from slug
 * Supports both old format (just UUID) and new format (keywords-bpm-uuid)
 */
export function extractUuidFromSlug(slug: string): string | null {
  // New format: keywords-bpm-uuid (extract last part after final hyphen)
  const parts = slug.split('-');
  const lastPart = parts[parts.length - 1];
  
  // Check if last part looks like a UUID (8+ characters, alphanumeric)
  if (lastPart && /^[a-f0-9]{8,}$/i.test(lastPart)) {
    return lastPart;
  }
  
  // Old format: check if entire slug is a UUID
  if (/^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(slug)) {
    return slug;
  }
  
  // Fallback: check if slug contains a UUID pattern anywhere
  const uuidMatch = slug.match(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/i);
  if (uuidMatch) {
    return uuidMatch[0];
  }
  
  return null;
}

/**
 * Generate track URL for linking
 */
export function generateTrackUrl(track: {
  prompt?: string;
  title?: string;
  bpm?: number;
  uuid: string;
  style?: string;
  slug?: string;
}, locale: string = "en"): string {
  // Use existing slug if available, otherwise generate new one
  const slug = track.slug || generateTrackSlug(track);
  
  const basePath = locale === "en" ? "" : `/${locale}`;
  return `${basePath}/loops/${slug}`;
}

/**
 * Validate and normalize slug for database storage
 */
export function normalizeSlug(slug: string): string {
  return slug
    .toLowerCase()
    .replace(/[^a-z0-9-]/g, '')
    .replace(/-+/g, '-')
    .replace(/^-+|-+$/g, '')
    .substring(0, 100); // Database limit
}

/**
 * Generate meta title for track page
 */
export function generateTrackMetaTitle(track: {
  title?: string;
  prompt?: string;
  bpm?: number;
  style?: string;
  duration?: number;
}): string {
  const name = track.title || track.prompt || "AI Music Loop";
  const bpm = track.bpm ? ` - ${track.bpm} BPM` : "";
  const style = track.style ? ` ${track.style}` : "";
  const duration = track.duration ? ` ${track.duration}s` : "";
  
  return `${name}${bpm}${style}${duration} Loop - LoopCraft`;
}

/**
 * Generate meta description for track page
 */
export function generateTrackMetaDescription(track: {
  prompt?: string;
  title?: string;
  style?: string;
  mood?: string;
  bpm?: number;
  duration?: number;
  is_seamless?: boolean;
}): string {
  const name = track.title || track.prompt || "AI-generated music loop";
  const style = track.style || "music";
  const mood = track.mood ? ` ${track.mood}` : "";
  const bpm = track.bpm || "variable";
  const duration = track.duration || "seamless";
  const seamless = track.is_seamless ? "Verified seamless loop" : "Music loop";
  
  return `${name} - ${duration}s${mood} ${style} loop at ${bpm} BPM. ${seamless} perfect for content creators, videos, and background music. Royalty-free with commercial license.`;
}

/**
 * Generate keywords for track SEO
 */
export function generateTrackKeywords(track: {
  style?: string;
  mood?: string;
  bpm?: number;
  duration?: number;
  prompt?: string;
}): string[] {
  const keywords = [
    "AI music loop",
    "seamless loop", 
    "background music",
    "royalty free music",
    "content creation music",
    "loop music",
    "music for videos"
  ];
  
  if (track.style) keywords.push(track.style, `${track.style} music`, `${track.style} loop`);
  if (track.mood) keywords.push(track.mood, `${track.mood} music`);
  if (track.bpm) keywords.push(`${track.bpm} BPM`, `${track.bpm} BPM music`);
  if (track.duration) keywords.push(`${track.duration}s loop`, `${track.duration} second loop`);
  
  // Extract keywords from prompt
  if (track.prompt) {
    const promptKeywords = track.prompt
      .toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 3 && !['music', 'loop', 'track', 'song'].includes(word))
      .slice(0, 5); // Limit to 5 keywords from prompt
    
    keywords.push(...promptKeywords);
  }
  
  return [...new Set(keywords)]; // Remove duplicates
}
