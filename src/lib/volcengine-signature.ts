/**
 * Volcengine API 签名工具
 * 实现 HMAC-SHA256 签名算法
 */

import crypto from 'crypto';

export interface SignatureParams {
  headers: Record<string, string>;
  method: string;
  query: Record<string, string>;
  accessKeyId: string;
  secretAccessKey: string;
  serviceName: string;
  region: string;
  body: string;
  bodySha: string;
}

/**
 * 获取当前UTC时间字符串
 * 格式：YYYYMMDD'T'HHMMSS'Z'
 */
export function getDateTimeNow(): string {
  const now = new Date();
  const year = now.getUTCFullYear();
  const month = String(now.getUTCMonth() + 1).padStart(2, '0');
  const day = String(now.getUTCDate()).padStart(2, '0');
  const hour = String(now.getUTCHours()).padStart(2, '0');
  const minute = String(now.getUTCMinutes()).padStart(2, '0');
  const second = String(now.getUTCSeconds()).padStart(2, '0');
  
  return `${year}${month}${day}T${hour}${minute}${second}Z`;
}

/**
 * 获取短日期字符串
 * 格式：YYYYMMDD
 */
export function getShortDate(dateTime?: string): string {
  const dt = dateTime || getDateTimeNow();
  return dt.substring(0, 8);
}

/**
 * SHA256 哈希
 */
export function hash(data: string): string {
  return crypto.createHash('sha256').update(data, 'utf8').digest('hex');
}

/**
 * HMAC-SHA256 签名
 */
export function hmacSha256(key: Buffer | string, data: string): Buffer {
  return crypto.createHmac('sha256', key).update(data, 'utf8').digest();
}

/**
 * 构建规范化查询字符串
 */
function buildCanonicalQueryString(query: Record<string, string>): string {
  const sortedKeys = Object.keys(query).sort();
  const pairs: string[] = [];
  
  for (const key of sortedKeys) {
    const value = query[key];
    pairs.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
  }
  
  return pairs.join('&');
}

/**
 * 构建规范化头部字符串
 */
function buildCanonicalHeaders(headers: Record<string, string>): string {
  const filteredKeys = Object.keys(headers)
    .filter(key => !HEADER_KEYS_TO_IGNORE.has(key.toLowerCase()));

  const sortedKeys = filteredKeys
    .map(key => key.toLowerCase())
    .sort();

  const pairs: string[] = [];
  for (const key of sortedKeys) {
    const originalKey = Object.keys(headers).find(k => k.toLowerCase() === key);
    if (originalKey) {
      pairs.push(`${key}:${headers[originalKey].trim()}`);
    }
  }

  return pairs.join('\n') + '\n';
}

/**
 * 不参与签名的请求头
 */
const HEADER_KEYS_TO_IGNORE = new Set([
  "authorization",
  "content-type",
  "content-length",
  "user-agent",
  "presigned-expires",
  "expect",
]);

/**
 * 获取签名头部列表
 */
function getSignedHeaders(headers: Record<string, string>): string {
  return Object.keys(headers)
    .filter(key => !HEADER_KEYS_TO_IGNORE.has(key.toLowerCase()))
    .map(key => key.toLowerCase())
    .sort()
    .join(';');
}

/**
 * 构建规范化请求字符串
 */
function buildCanonicalRequest(
  method: string,
  path: string,
  queryString: string,
  canonicalHeaders: string,
  signedHeaders: string,
  payloadHash: string
): string {
  return [
    method,
    path,
    queryString,
    canonicalHeaders,
    signedHeaders,
    payloadHash
  ].join('\n');
}

/**
 * 构建待签名字符串
 */
function buildStringToSign(
  algorithm: string,
  requestDateTime: string,
  credentialScope: string,
  canonicalRequestHash: string
): string {
  return [
    algorithm,
    requestDateTime,
    credentialScope,
    canonicalRequestHash
  ].join('\n');
}

/**
 * 计算签名密钥
 */
function getSigningKey(
  secretAccessKey: string,
  dateStamp: string,
  regionName: string,
  serviceName: string
): Buffer {
  // 使用工作代码的签名密钥生成方式
  const kDate = hmacSha256(secretAccessKey, dateStamp);
  const kRegion = hmacSha256(kDate, regionName);
  const kService = hmacSha256(kRegion, serviceName);
  const kSigning = hmacSha256(kService, 'request');
  return kSigning;
}

/**
 * 生成 Volcengine API 签名
 */
export function sign(params: SignatureParams): string {
  const {
    headers,
    method,
    query,
    accessKeyId,
    secretAccessKey,
    serviceName,
    region,
    bodySha
  } = params;

  const algorithm = 'HMAC-SHA256';
  const requestDateTime = headers['X-Date'];
  const dateStamp = getShortDate(requestDateTime);
  
  // 构建凭证范围
  const credentialScope = `${dateStamp}/${region}/${serviceName}/request`;
  
  // 构建规范化请求
  const canonicalQueryString = buildCanonicalQueryString(query);
  const canonicalHeaders = buildCanonicalHeaders(headers);
  const signedHeaders = getSignedHeaders(headers);
  
  const canonicalRequest = buildCanonicalRequest(
    method,
    '/',
    canonicalQueryString,
    canonicalHeaders,
    signedHeaders,
    bodySha
  );
  
  // 构建待签名字符串
  const canonicalRequestHash = hash(canonicalRequest);
  const stringToSign = buildStringToSign(
    algorithm,
    requestDateTime,
    credentialScope,
    canonicalRequestHash
  );
  
  // 计算签名
  const signingKey = getSigningKey(secretAccessKey, dateStamp, region, serviceName);
  const signature = hmacSha256(signingKey, stringToSign).toString('hex');
  
  // 构建 Authorization 头部
  return `${algorithm} Credential=${accessKeyId}/${credentialScope}, SignedHeaders=${signedHeaders}, Signature=${signature}`;
}
